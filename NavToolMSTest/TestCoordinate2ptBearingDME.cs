﻿// using System.Reflection;
// using Allure.Net.Commons;
// using Allure.Xunit;
// using Allure.Xunit.Attributes;
// using Allure.Xunit.Attributes.Steps;
// using Newtonsoft.Json;
// using Xunit;

// namespace Xunitest
// {
//     public class TestCoordinate2ptBearingDME : IDisposable
//     {
//         private static MethodInfo? TwoptBearingDME;
//         private static MethodInfo? btn_test;
//         private static MethodInfo? InitializeComponent;
//         private static Type? UCConverter;
//         private static Type? UCConverter_result;

//         [AllureBefore("Setup test context")]
//         public TestCoordinate2ptBearingDME()
//         {
//             string exePath =
//                 @"..\..\..\navtool\Debug\Navtool.exe";
//             var assembly = Assembly.LoadFrom(exePath);
//             Console.WriteLine(assembly.ToString());

//             // 获取 UCConverter 类型
//             UCConverter = assembly.GetType("NavTool.UserControls.UCConverter");
//             if (UCConverter == null)
//             {
//                 throw new InvalidOperationException("UCConverter type not found in the assembly.");
//             }

//             // 获取 UCConverter_result 类型 (赋值给类字段)
//             UCConverter_result = assembly.GetType("NavTool.UserControls.UCConverter_result");
//             if (UCConverter_result == null)
//             {
//                 throw new InvalidOperationException(
//                     "UCConverter_result type not found in the assembly."
//                 );
//             }

//             Console.WriteLine(UCConverter.Assembly.Location);

//             // 获取所有方法用于调试
//             var methods = UCConverter.GetMethods(
//                 BindingFlags.Instance
//                     | BindingFlags.Public
//                     | BindingFlags.NonPublic
//                     | BindingFlags.Static
//                     | BindingFlags.DeclaredOnly
//             );

//             foreach (var m in methods)
//             {
//                 Console.WriteLine($"{m.Name} ({m.GetParameters().Length} parameters)");
//             }

//             // 获取目标方法并赋值给类字段
//             TwoptBearingDME = UCConverter.GetMethod("btnConvert_Click_2ptBD",
//                 BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic |
//                 BindingFlags.Static | BindingFlags.DeclaredOnly,
//                 null,
//                 new Type[]
//                 {
//                     typeof(string), typeof(string), typeof(string), typeof(string),
//                     typeof(string), typeof(string), typeof(string), typeof(string),
//                     typeof(string), typeof(string), typeof(string), typeof(string),
//                     typeof(string), typeof(string), typeof(string), typeof(string),
//                     typeof(string), typeof(string), typeof(string), typeof(string),
//                     typeof(string), typeof(string), typeof(string), typeof(int),
//                     typeof(string), typeof(int),
//                     typeof(string), typeof(string), typeof(string), typeof(string)
//                 },
//                 null
//             );

//             if (TwoptBearingDME != null)
//             {
//                 Console.WriteLine("✅ 方法找到了: " + TwoptBearingDME.Name);
//                 Console.WriteLine("返回类型: " + TwoptBearingDME.ReturnType);
//             }
//             else
//             {
//                 Console.WriteLine("❌ 方法找不到");
//                 throw new InvalidOperationException("Target method not found");
//             }

//             // 获取目标方法并赋值给类字段
//             InitializeComponent = UCConverter.GetMethod("InitializeComponent",
//                 BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly,
//                 null,
//                 new Type[]{ },
//                 null
//             );

//             if (InitializeComponent != null)
//             {
//                 Console.WriteLine("✅ 方法找到了: " + InitializeComponent.Name);
//                 Console.WriteLine("返回类型: " + InitializeComponent.ReturnType);
//             }
//             else
//             {
//                 Console.WriteLine("❌ 方法找不到");
//                 throw new InvalidOperationException("Target method not found");
//             }
//         }

//         public void Dispose()
//         {
//             Console.WriteLine("Dispose");
//         }

//         [Fact(DisplayName = "NTI-NDBCT-TC-001")]
//         [AllureDescription("验证正向计算在有效输入时返回正确的经纬度结果。")]
//         [AllureSeverity(SeverityLevel.critical)]
//         [AllureTag("Vincenty", "Forward Calculation")]
//         public void Forward_ValidInput_ReturnsCorrectResult()
//         {
//             // 确保所有必要的类型和方法都已初始化
//             if (UCConverter == null) throw new InvalidOperationException("UCConverter type is null");
//             if (TwoptBearingDME == null) throw new InvalidOperationException("Target method is null");
//             if (UCConverter_result == null) throw new InvalidOperationException("Result type is null");

//             // 创建有效测试数据 - 使用真实值替换空字符串
//             // 这里只是示例值，您应该使用真实的测试数据
//             String vtxtRefLatDeg = "52"; // 纬度度
//             String vtxtRefLatMin = "54";
//             String vtxtRefLatSec = "13";
//             String vtxtRefLatSecDecimal = "00";
//             String vtxtRefLonDeg = "122"; // 经度度
//             String vtxtRefLonMin = "26";
//             String vtxtRefLonSec = "42";
//             String vtxtRefLonSecDecimal = "00";
//             String vtxtRefLatDeg2 = "52"; // 第二点纬度度
//             String vtxtRefLatMin2 = "55";
//             String vtxtRefLatSec2 = "14";
//             String vtxtRefLatSecDecimal2 = "00";
//             String vtxtRefLonDeg2 = "122"; // 第二点经度度
//             String vtxtRefLonMin2 = "24";
//             String vtxtRefLonSec2 = "52";
//             String vtxtRefLonSecDecimal2 = "00";
//             String vtxtDis = "10"; // 距离
//             String vcomboDistUnit = "nm"; // 距离单位
//             String vtxtDis2 = "320.54";
//             String vcomboDistUnit2 = "m";
//             String vtxtBearing = "328"; // 方位角
//             String vtxtBearing2 = "10";
//             String vtxtMagVar = "12.6"; // 磁偏角
//             int vcomboMagVar = 0; // 选择索引
//             String vtxtMagVar2 = "10";
//             int vcomboMagVar2 = 0;
//             String vcomboDistUnit12 = "m";
//             String vcomboDistUnit13 = "m";
//             String vcomboDistUnit14 = "m";
//             String vcomboDistUnit23 = "m";
//             object[] parameters = {
//                 vtxtRefLatDeg, vtxtRefLatMin, vtxtRefLatSec, vtxtRefLatSecDecimal,
//                 vtxtRefLonDeg, vtxtRefLonMin, vtxtRefLonSec, vtxtRefLonSecDecimal,
//                 vtxtRefLatDeg2, vtxtRefLatMin2, vtxtRefLatSec2, vtxtRefLatSecDecimal2,
//                 vtxtRefLonDeg2, vtxtRefLonMin2, vtxtRefLonSec2, vtxtRefLonSecDecimal2,
//                 vtxtDis, vcomboDistUnit, vtxtDis2, vcomboDistUnit2,
//                 vtxtBearing, vtxtBearing2, vtxtMagVar, vcomboMagVar, vtxtMagVar2, vcomboMagVar2,
//                 vcomboDistUnit12,vcomboDistUnit13,vcomboDistUnit14,vcomboDistUnit23
//             };

//             // 创建实例
//             var converter = Activator.CreateInstance(UCConverter);
//             if (converter == null)
//             {
//                 throw new InvalidOperationException("Failed to create converter instance");
//             }

//             try
//             {
//                 InitializeComponent.Invoke(converter,null);
//                 // 调用方法
//                 object resultObj = TwoptBearingDME.Invoke(converter, parameters);

//                 if (resultObj == null)
//                 {
//                     throw new InvalidOperationException("Method returned null");
//                 }
//                 dynamic result = resultObj;
//                 Console.WriteLine($"Lat: {result.vtxtResLatDeg}, Lon: {result.vtxtResLonDeg}");
//             }
//             catch (TargetInvocationException ex)
//             {
//                 // 捕获方法内部抛出的实际异常
//                 throw new Exception($"Method invocation failed: {ex.InnerException?.Message}", ex.InnerException);
//             }
//         }
//     }
// }