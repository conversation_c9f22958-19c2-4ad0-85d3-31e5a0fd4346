using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Linq;
using System.Text;
using Allure.Net.Commons;
using Allure.Xunit;
using Allure.Xunit.Attributes;
using Allure.Xunit.Attributes.Steps;
using Newtonsoft.Json;
using Xunit;

namespace Xunitest
{
    public class TestCoordinate : IDisposable
    {
        #region 常量定义
        private const string NAVTOOL_EXE_PATH = @"..\..\..\navtool\Debug\Navtool.exe"; // NavTool可执行文件路径
        private const string TEST_DATA_PATH = @"..\..\..\TestData\ConvertedCoordinateTestCases.json"; // 测试数据文件路径
        private const string UC_CONVERTER_TYPE = "NavTool.UserControls.UCConverter"; // 坐标转换器用户控件类型
        private const string UC_CONVERTER_RESULT_TYPE = "NavTool.UserControls.UCConverter_result"; // 坐标转换器结果类型
        private const string UC_CONVERTER_INPUT_TYPE = "NavTool.UserControls.UCConverter_input"; // 坐标转换器输入类型
        private const string INITIALIZE_COMPONENT_METHOD = "InitializeComponent"; // 初始化组件方法名
        private const double COORDINATE_PRECISION_TOLERANCE = 5.0; // 坐标精度容差值（秒）
        #endregion

        #region 私有字段
        private readonly Dictionary<string, MethodInfo> _methodsToTest = new Dictionary<string, MethodInfo>();
        private Type _ucConverterType;
        private Type _ucConverterResultType;
        private Type _ucConverterInputType;
        private MethodInfo _initializeComponentMethod;
        private static TestData _cachedTestData;

        // 静态缓存字段，避免重复加载程序集和类型
        private static Assembly _cachedAssembly;
        private static Type _cachedUcConverterType;
        private static Type _cachedUcConverterResultType;
        private static Type _cachedUcConverterInputType;
        private static MethodInfo _cachedInitializeComponentMethod;
        private static readonly Dictionary<string, MethodInfo> _cachedMethodsToTest = new Dictionary<string, MethodInfo>();
        private static readonly object _assemblyLock = new object();
        private static bool _isInitialized = false;
        #endregion

        #region 数据模型
        public class TestData
        {
            public List<MethodTest> MethodTests { get; set; } = new List<MethodTest>();
        }

        public class MethodTest
        {
            public string MethodName { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public List<TestCase> TestCases { get; set; } = new List<TestCase>();
        }

        public class TestCase
        {
            public string TestCaseId { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Keyword { get; set; } = string.Empty;
            public dynamic Input { get; set; }
            public dynamic ExpectedOutput { get; set; }
        }

        public class TestCaseData
        {
            public string TestCaseId { get; set; }
            public string Description { get; set; }
            public string MethodName { get; set; }
            public string[] Keywords { get; set; }
            public dynamic Input { get; set; }
            public dynamic ExpectedOutput { get; set; }
        }
        #endregion

        #region 静态方法和测试数据
        /// <summary>
        /// 加载测试数据（带缓存）
        /// </summary>
        public static TestData LoadTestData()
        {
            if (_cachedTestData != null)
            {
                return _cachedTestData;
            }

            string jsonPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, TEST_DATA_PATH);
            string jsonContent = File.ReadAllText(jsonPath);
            _cachedTestData = JsonConvert.DeserializeObject<TestData>(jsonContent) ?? new TestData();
            return _cachedTestData;
        }

        /// <summary>
        /// 为xUnit Theory提供测试用例数据
        /// </summary>
        public static IEnumerable<object[]> GetTestCases()
        {
            var testData = LoadTestData();
            foreach (var methodTest in testData.MethodTests)
            {
                foreach (var testCase in methodTest.TestCases)
                {
                    // 解析Keyword字段，如果为空则使用空数组
                    string[] keywords = string.IsNullOrEmpty(testCase.Keyword)
                        ? Array.Empty<string>()
                        : testCase.Keyword.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                         .Select(k => k.Trim())
                                         .ToArray();

                    yield return new object[]
                    {
                        new TestCaseData
                        {
                            TestCaseId = testCase.TestCaseId,
                            Description = testCase.Description,
                            MethodName = methodTest.MethodName,
                            Keywords = keywords,
                            Input = testCase.Input,
                            ExpectedOutput = testCase.ExpectedOutput
                        }
                    };
                }
            }
        }
        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化所有反射相关的类型和方法（使用静态缓存避免重复加载）
        /// </summary>
        private void InitializeReflectionComponents()
        {
            // 使用双检锁模式确保线程安全的单次初始化
            if (!_isInitialized)
            {
                lock (_assemblyLock)
                {
                    if (!_isInitialized)
                    {
                        // 加载程序集
                        string exePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, NAVTOOL_EXE_PATH);
                        _cachedAssembly = Assembly.LoadFrom(exePath);
                        Console.WriteLine($"首次加载程序集: {_cachedAssembly}");

                        // 获取类型
                        _cachedUcConverterType = _cachedAssembly.GetType(UC_CONVERTER_TYPE) ?? throw new InvalidOperationException($"未找到类型: {UC_CONVERTER_TYPE}");
                        _cachedUcConverterResultType = _cachedAssembly.GetType(UC_CONVERTER_RESULT_TYPE) ?? throw new InvalidOperationException($"未找到类型: {UC_CONVERTER_RESULT_TYPE}");
                        _cachedUcConverterInputType = _cachedAssembly.GetType(UC_CONVERTER_INPUT_TYPE) ?? throw new InvalidOperationException($"未找到类型: {UC_CONVERTER_INPUT_TYPE}");

                        // 获取初始化方法
                        _cachedInitializeComponentMethod = _cachedUcConverterType.GetMethod(INITIALIZE_COMPONENT_METHOD,
                            BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly,
                            null, Type.EmptyTypes, null) ?? throw new InvalidOperationException($"未找到方法: {INITIALIZE_COMPONENT_METHOD}");

                        // 发现测试方法
                        var testData = LoadTestData();
                        var methodNames = testData.MethodTests?.Select(mt => mt.MethodName).Distinct().ToArray() ?? Array.Empty<string>();
                        Console.WriteLine($"发现 {methodNames.Length} 个测试方法");

                        var allMethods = _cachedUcConverterType.GetMethods(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static | BindingFlags.DeclaredOnly);
                        foreach (string methodName in methodNames)
                        {
                            var method = allMethods.FirstOrDefault(m => m.Name == methodName &&
                                m.GetParameters().Length == 1 &&
                                (m.GetParameters()[0].ParameterType == _cachedUcConverterInputType || m.GetParameters()[0].ParameterType.IsAssignableFrom(_cachedUcConverterInputType)));

                            if (method != null)
                            {
                                _cachedMethodsToTest.Add(methodName, method);
                                Console.WriteLine($"已注册: {method.Name}");
                            }
                            else
                            {
                                Console.WriteLine($"未找到: {methodName}");
                            }
                        }

                        _isInitialized = true;
                        Console.WriteLine("反射组件初始化完成，已缓存所有类型和方法");
                    }
                }
            }

            // 将缓存的值赋给实例字段
            _ucConverterType = _cachedUcConverterType;
            _ucConverterResultType = _cachedUcConverterResultType;
            _ucConverterInputType = _cachedUcConverterInputType;
            _initializeComponentMethod = _cachedInitializeComponentMethod;

            // 复制缓存的方法到实例字典
            _methodsToTest.Clear();
            foreach (var kvp in _cachedMethodsToTest)
            {
                _methodsToTest.Add(kvp.Key, kvp.Value);
            }
        }

        /// <summary>
        /// 执行单个坐标计算测试用例
        /// </summary>
        private void ExecuteCoordinateTest(TestCaseData testCaseData)
        {
            Console.WriteLine($"开始测试: {testCaseData.TestCaseId} - {testCaseData.Description}");

            // 检查方法是否存在
            if (!_methodsToTest.ContainsKey(testCaseData.MethodName))
            {
                throw new InvalidOperationException($"未找到方法: {testCaseData.MethodName}");
            }

            var methodToTest = _methodsToTest[testCaseData.MethodName];

            // 创建实例
            var converter = Activator.CreateInstance(_ucConverterType);
            if (converter == null)
            {
                throw new InvalidOperationException("创建转换器实例失败");
            }

            try
            {
                // 初始化组件
                _initializeComponentMethod.Invoke(converter, null);

                // 使用新的参数创建方法
                object[] parameters = CreateMethodParameters(methodToTest, testCaseData.Input);

                // 调用方法
                object resultObj = methodToTest.Invoke(converter, parameters);

                if (resultObj == null)
                {
                    throw new InvalidOperationException("方法返回null");
                }

                // 输出结果
                dynamic result = resultObj;
                dynamic expectedOutput = testCaseData.ExpectedOutput;

                // 打印完整输出json
                Console.WriteLine($"输出结果: {JsonConvert.SerializeObject(result)}");

                // 验证结果并进行断言
                bool isMatch = ValidateCoordinateResult(result, expectedOutput);

                // 独立的Assert
                Assert.True(isMatch,
                    $"测试用例 {testCaseData.TestCaseId} 失败: 经纬度结果不匹配预期值。\n" +
                    $"预期: Lat={expectedOutput.vtxtResLatDeg}°{expectedOutput.vtxtResLatMin}'{expectedOutput.vtxtResLatSec}\", " +
                    $"Lon={expectedOutput.vtxtResLonDeg}°{expectedOutput.vtxtResLonMin}'{expectedOutput.vtxtResLonSec}\"\n" +
                    $"实际结果: {JsonConvert.SerializeObject(result)}");
            }
            catch (TargetInvocationException ex)
            {
                // 捕获方法内部抛出的实际异常
                throw new Exception($"测试用例 {testCaseData.TestCaseId} 执行失败: {ex.InnerException?.Message}", ex.InnerException);
            }
            catch (Exception ex)
            {
                // 捕获其他异常
                throw new Exception($"测试用例 {testCaseData.TestCaseId} 执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证坐标计算结果
        /// </summary>
        private bool ValidateCoordinateResult(dynamic actualResult, dynamic expectedOutput)
        {
            try
            {
                // 检查null值
                bool expectedIsNull = expectedOutput.vtxtResLatDeg.ToString() == "null" ||
                                    expectedOutput.vtxtResLatMin.ToString() == "null" ||
                                    expectedOutput.vtxtResLatSec.ToString() == "null";

                if (expectedIsNull)
                {
                    bool actualIsNull = (actualResult.vtxtResLatDeg == null || actualResult.vtxtResLatDeg.ToString() == "null");
                    Console.WriteLine(actualIsNull ? "null值验证成功" : "预期为null，但实际输出不为null");
                    return actualIsNull;
                }

                // 精度验证
                double expectedLatSec = Convert.ToDouble(expectedOutput.vtxtResLatSec.ToString());
                double expectedLonSec = Convert.ToDouble(expectedOutput.vtxtResLonSec.ToString());

                // 检查第一组坐标
                if (CheckCoordinateMatch(actualResult, expectedOutput, expectedLatSec, expectedLonSec, false))
                {
                    Console.WriteLine("精度验证成功 - 第一组");
                    return true;
                }

                // 检查第二组坐标
                if (actualResult.vtxtResLatDeg2 != null &&
                    CheckCoordinateMatch(actualResult, expectedOutput, expectedLatSec, expectedLonSec, true))
                {
                    Console.WriteLine("精度验证成功 - 第二组");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查坐标匹配
        /// </summary>
        private bool CheckCoordinateMatch(dynamic result, dynamic expected, double expectedLatSec, double expectedLonSec, bool useSecond)
        {
            var (latDeg, latMin, latSec, lonDeg, lonMin, lonSec) = useSecond ?
                (result.vtxtResLatDeg2.ToString(), result.vtxtResLatMin2.ToString(), Convert.ToDouble(result.vtxtResLatSec2.ToString()),
                 result.vtxtResLonDeg2.ToString(), result.vtxtResLonMin2.ToString(), Convert.ToDouble(result.vtxtResLonSec2.ToString())) :
                (result.vtxtResLatDeg.ToString(), result.vtxtResLatMin.ToString(), Convert.ToDouble(result.vtxtResLatSec.ToString()),
                 result.vtxtResLonDeg.ToString(), result.vtxtResLonMin.ToString(), Convert.ToDouble(result.vtxtResLonSec.ToString()));

            return expected.vtxtResLatDeg.ToString() == latDeg &&
                   expected.vtxtResLatMin.ToString() == latMin &&
                   expected.vtxtResLonDeg.ToString() == lonDeg &&
                   expected.vtxtResLonMin.ToString() == lonMin &&
                   Math.Abs(expectedLatSec - latSec) < COORDINATE_PRECISION_TOLERANCE &&
                   Math.Abs(expectedLonSec - lonSec) < COORDINATE_PRECISION_TOLERANCE;
        }
        #endregion

        [AllureBefore("Setup test context")]
        public TestCoordinate()
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;
            InitializeReflectionComponents();
        }

        public void Dispose()
        {
            // 清理实例相关的缓存数据
            _methodsToTest?.Clear();
            Console.WriteLine("实例资源已释放");

            // 注意：静态缓存字段 (_cachedAssembly, _cachedUcConverterType 等)
            // 不在这里清理，因为它们应该在整个测试会话期间保持有效
        }

        /// <summary>
        /// 清理所有静态缓存（可在测试会话结束时调用）
        /// </summary>
        public static void ClearStaticCache()
        {
            lock (_assemblyLock)
            {
                _cachedAssembly = null;
                _cachedUcConverterType = null;
                _cachedUcConverterResultType = null;
                _cachedUcConverterInputType = null;
                _cachedInitializeComponentMethod = null;
                _cachedMethodsToTest?.Clear();
                _cachedTestData = null;
                _isInitialized = false;
                Console.WriteLine("所有静态缓存已清理");
            }
        }

        /// <summary>
        /// 创建方法参数对象
        /// </summary>
        private object[] CreateMethodParameters(MethodInfo methodInfo, dynamic testInput)
        {
            var parameters = methodInfo.GetParameters();
            var parameterObjects = new object[parameters.Length];

            for (int i = 0; i < parameters.Length; i++)
            {
                try
                {
                    // 尝试JSON反序列化
                    string jsonInput = JsonConvert.SerializeObject(testInput);
                    var parameterObject = JsonConvert.DeserializeObject(jsonInput, parameters[i].ParameterType);

                    if (parameterObject != null)
                    {
                        parameterObjects[i] = parameterObject;
                        Console.WriteLine($"成功创建参数 {i}: {parameters[i].ParameterType.Name}");
                    }
                    else
                    {
                        parameterObjects[i] = CreateParameterObjectWithReflection(parameters[i].ParameterType, testInput);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"参数创建失败，使用反射: {ex.Message}");
                    parameterObjects[i] = CreateParameterObjectWithReflection(parameters[i].ParameterType, testInput);
                }
            }

            return parameterObjects;
        }

        /// <summary>
        /// 使用反射创建参数对象并设置属性值
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <param name="testInput">测试输入数据</param>
        /// <returns>创建的参数对象</returns>
        private object CreateParameterObjectWithReflection(Type targetType, dynamic testInput)
        {
            // 创建目标类型实例
            object parameterObject = Activator.CreateInstance(targetType);

            // 初始化所有字符串属性为空字符串，避免null引用异常
            foreach (var prop in targetType.GetProperties())
            {
                if (prop.PropertyType == typeof(string) && prop.CanWrite)
                {
                    prop.SetValue(parameterObject, "");
                }
            }

            // 从测试数据中设置属性值
            try
            {
                // 尝试将动态对象转换为字典
                IDictionary<string, object> inputDict = testInput as IDictionary<string, object>;
                if (inputDict != null)
                {
                    SetPropertiesFromDictionary(parameterObject, targetType, inputDict);
                }
                else
                {
                    // 如果无法转换为字典，使用反射遍历属性
                    SetPropertiesFromObject(parameterObject, targetType, testInput);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置属性时出错: {ex.Message}");
            }

            return parameterObject;
        }

        /// <summary>
        /// 从字典设置对象属性
        /// </summary>
        private void SetPropertiesFromDictionary(object target, Type targetType, IDictionary<string, object> sourceDict)
        {
            foreach (var kvp in sourceDict)
            {
                var targetProp = targetType.GetProperty(kvp.Key);
                if (targetProp != null && targetProp.CanWrite)
                {
                    try
                    {
                        object convertedValue = ConvertValue(kvp.Value, targetProp.PropertyType);
                        targetProp.SetValue(target, convertedValue);
                        Console.WriteLine($"set property {kvp.Key} = {kvp.Value}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"set property{kvp.Key} failed: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 从对象设置属性
        /// </summary>
        private void SetPropertiesFromObject(object target, Type targetType, dynamic source)
        {
            foreach (var sourceProp in source.GetType().GetProperties())
            {
                try
                {
                    var value = sourceProp.GetValue(source);
                    var targetProp = targetType.GetProperty(sourceProp.Name);
                    if (targetProp != null && targetProp.CanWrite && value != null)
                    {
                        object convertedValue = ConvertValue(value, targetProp.PropertyType);
                        targetProp.SetValue(target, convertedValue);
                        Console.WriteLine($"通过反射设置属性 {sourceProp.Name} = {value}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"通过反射设置属性 {sourceProp.Name} 失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 转换值到目标类型
        /// </summary>
        private object ConvertValue(object value, Type targetType)
        {
            if (value == null)
                return targetType.IsValueType ? Activator.CreateInstance(targetType) : null;

            if (targetType.IsAssignableFrom(value.GetType()))
                return value;

            // 处理可空类型
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                targetType = Nullable.GetUnderlyingType(targetType);
            }

            // 使用Convert.ChangeType进行类型转换
            return Convert.ChangeType(value, targetType);
        }

        /// <summary>
        /// 为测试用例动态添加Allure标签
        /// </summary>
        private void AddAllureTags(string[] keywords)
        {
            if (keywords != null && keywords.Length > 0)
            {
                foreach (var keyword in keywords)
                {
                    if (!string.IsNullOrWhiteSpace(keyword))
                    {
                        AllureApi.AddLabel("tag", keyword.Trim());
                    }
                }
            }
        }

        [Theory]
        [MemberData(nameof(GetTestCases))]
        [AllureDescription("执行坐标计算测试")]
        [AllureSeverity(SeverityLevel.critical)]
        public void CoordinateCalculationTest(TestCaseData testCaseData)
        {
            try
            {
                // 动态添加基于Keyword字段的AllureTag
                AddAllureTags(testCaseData.Keywords);

                // 添加测试用例信息到Allure报告
                AllureApi.SetTestName($"{testCaseData.TestCaseId}: {testCaseData.Description}");

                // 设置唯一的测试用例ID，确保每个测试用例在报告中都能独立显示
                AllureApi.AddLabel("testId", testCaseData.TestCaseId);
                AllureApi.AddLabel("uniqueId", $"{testCaseData.TestCaseId}_{testCaseData.MethodName}_{DateTime.Now.Ticks}");

                // 执行单个测试用例
                ExecuteCoordinateTest(testCaseData);

                Console.WriteLine($"测试用例 {testCaseData.TestCaseId} 执行成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试用例 {testCaseData.TestCaseId} 执行失败: {ex.Message}");
                throw;
            }
        }

    }
}