{"MethodTests": [{"MethodName": "btnConvert_Click_1ptBD", "Description": "单点方位距离计算", "TestCases": [{"TestCaseId": "NTI-NDBCT-TC-101", "Description": "验证单点方位距离计算在有效输入时返回正确的经纬度结果", "Input": {"vtxtRefLatDeg": "39", "vtxtRefLatMin": "28", "vtxtRefLatSec": "07", "vtxtRefLatSecDecimal": "60", "vtxtRefLonDeg": "116", "vtxtRefLonMin": "25", "vtxtRefLonSec": "54", "vtxtRefLonSecDecimal": "20", "vtxtRefLatDeg2": "", "vtxtRefLatMin2": "", "vtxtRefLatSec2": "", "vtxtRefLatSecDecimal2": "", "vtxtRefLonDeg2": "", "vtxtRefLonMin2": "", "vtxtRefLonSec2": "", "vtxtRefLonSecDecimal2": "", "vtxtDis": "3685", "vcomboDistUnit": "m", "vtxtDis2": "", "vcomboDistUnit2": "", "vtxtBearing": "1", "vtxtBearing2": "", "vtxtMagVar": "7.5", "vcomboMagVar": 0, "vtxtMagVar2": "", "vcomboMagVar2": 0, "vcomboDistUnit12": "m", "vcomboDistUnit13": "m", "vcomboDistUnit14": "m", "vcomboDistUnit23": "m"}, "ExpectedOutput": {"vtxtResLatDeg": "39", "vtxtResLatMin": "30", "vtxtResLatSec": "06.20", "vtxtResLonDeg": "116", "vtxtResLonMin": "25", "vtxtResLonSec": "35.50"}}]}, {"MethodName": "btnConvert_Click_2ptBD", "Description": "两点方位距离计算", "TestCases": [{"TestCaseId": "NTI-NDBCT-TC-001", "Description": "validate two points distance bearing calculation", "Input": {"vtxtRefLatDeg": "52", "vtxtRefLatMin": "54", "vtxtRefLatSec": "13", "vtxtRefLatSecDecimal": "00", "vtxtRefLonDeg": "122", "vtxtRefLonMin": "26", "vtxtRefLonSec": "42", "vtxtRefLonSecDecimal": "00", "vtxtRefLatDeg2": "52", "vtxtRefLatMin2": "55", "vtxtRefLatSec2": "14", "vtxtRefLatSecDecimal2": "00", "vtxtRefLonDeg2": "122", "vtxtRefLonMin2": "24", "vtxtRefLonSec2": "52", "vtxtRefLonSecDecimal2": "00", "vtxtDis": "10", "vcomboDistUnit": "nm", "vtxtDis2": "320.54", "vcomboDistUnit2": "m", "vtxtBearing": "328", "vtxtBearing2": "10", "vtxtMagVar": "12.6", "vcomboMagVar": 0, "vtxtMagVar2": "10", "vcomboMagVar2": 0, "vcomboDistUnit12": "m", "vcomboDistUnit13": "m", "vcomboDistUnit14": "m", "vcomboDistUnit23": "m"}, "ExpectedOutput": {"vtxtResLatDeg": "53", "vtxtResLatMin": "3", "vtxtResLatSec": "25.00", "vtxtResLonDeg": "122", "vtxtResLonMin": "32", "vtxtResLonSec": "10.00"}}, {"TestCaseId": "NTI-NDBCT-TC-002", "Description": "validate anothore point", "Input": {"vtxtRefLatDeg": "40", "vtxtRefLatMin": "30", "vtxtRefLatSec": "00", "vtxtRefLatSecDecimal": "00", "vtxtRefLonDeg": "116", "vtxtRefLonMin": "25", "vtxtRefLonSec": "00", "vtxtRefLonSecDecimal": "00", "vtxtRefLatDeg2": "40", "vtxtRefLatMin2": "32", "vtxtRefLatSec2": "00", "vtxtRefLatSecDecimal2": "00", "vtxtRefLonDeg2": "116", "vtxtRefLonMin2": "28", "vtxtRefLonSec2": "00", "vtxtRefLonSecDecimal2": "00", "vtxtDis": "5", "vcomboDistUnit": "km", "vtxtDis2": "200", "vcomboDistUnit2": "m", "vtxtBearing": "45", "vtxtBearing2": "15", "vtxtMagVar": "5.2", "vcomboMagVar": 0, "vtxtMagVar2": "5", "vcomboMagVar2": 0, "vcomboDistUnit12": "m", "vcomboDistUnit13": "m", "vcomboDistUnit14": "m", "vcomboDistUnit23": "m"}, "ExpectedOutput": {"vtxtResLatDeg": "40", "vtxtResLatMin": "32", "vtxtResLatSec": "45.00", "vtxtResLonDeg": "116", "vtxtResLonMin": "29", "vtxtResLonSec": "30.00"}}]}]}