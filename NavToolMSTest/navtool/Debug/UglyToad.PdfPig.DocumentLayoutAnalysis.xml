<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UglyToad.PdfPig.DocumentLayoutAnalysis</name>
    </assembly>
    <members>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering">
            <summary>
            Clustering Algorithms.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.NearestNeighbours``1(System.Collections.Generic.IReadOnlyList{``0},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Func{``0,``0,System.Double},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{``0,System.Boolean},System.Func{``0,``0,<PERSON>.Boolean},System.Int32)">
            <summary>
            Algorithm to group elements using nearest neighbours.
            <para>Uses the nearest neighbour as candidate.</para>
            </summary>
            <typeparam name="T">Letter, Word, TextLine, etc.</typeparam>
            <param name="elements">Elements to group.</param>
            <param name="distMeasure">The distance measure between two points.</param>
            <param name="maxDistanceFunction">The function that determines the maximum distance between two points in the same cluster.</param>
            <param name="pivotPoint">The pivot's point to use for pairing, e.g. BottomLeft, TopLeft.</param>
            <param name="candidatesPoint">The candidates' point to use for pairing, e.g. BottomLeft, TopLeft.</param>
            <param name="filterPivot">Filter to apply to the pivot point. If false, point will not be paired at all, e.g. is white space.</param>
            <param name="filterFinal">Filter to apply to both the pivot and the paired point. If false, point will not be paired at all, e.g. pivot and paired point have same font.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.NearestNeighbours``1(System.Collections.Generic.IReadOnlyList{``0},System.Int32,System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Func{``0,``0,System.Double},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{``0,System.Boolean},System.Func{``0,``0,System.Boolean},System.Int32)">
            <summary>
            Algorithm to group elements using nearest neighbours.
            <para>Uses the k-nearest neighbours as candidates.</para>
            </summary>
            <typeparam name="T">Letter, Word, TextLine, etc.</typeparam>
            <param name="elements">Elements to group.</param>
            <param name="k">The k-nearest neighbours to consider as candidates.</param>
            <param name="distMeasure">The distance measure between two points.</param>
            <param name="maxDistanceFunction">The function that determines the maximum distance between two points in the same cluster.</param>
            <param name="pivotPoint">The pivot's point to use for pairing, e.g. BottomLeft, TopLeft.</param>
            <param name="candidatesPoint">The candidates' point to use for pairing, e.g. BottomLeft, TopLeft.</param>
            <param name="filterPivot">Filter to apply to the pivot point. If false, point will not be paired at all, e.g. is white space.</param>
            <param name="filterFinal">Filter to apply to both the pivot and the paired point. If false, point will not be paired at all, e.g. pivot and paired point have same font.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.NearestNeighbours``1(System.Collections.Generic.IReadOnlyList{``0},System.Func{UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.Core.PdfLine,System.Double},System.Func{``0,``0,System.Double},System.Func{``0,UglyToad.PdfPig.Core.PdfLine},System.Func{``0,UglyToad.PdfPig.Core.PdfLine},System.Func{``0,System.Boolean},System.Func{``0,``0,System.Boolean},System.Int32)">
            <summary>
            Algorithm to group elements using nearest neighbours.
            </summary>
            <typeparam name="T">Letter, Word, TextLine, etc.</typeparam>
            <param name="elements">Array of elements to group.</param>
            <param name="distMeasure">The distance measure between two lines.</param>
            <param name="maxDistanceFunction">The function that determines the maximum distance between two points in the same cluster.</param>
            <param name="pivotLine">The pivot's line to use for pairing.</param>
            <param name="candidatesLine">The candidates' line to use for pairing.</param>
            <param name="filterPivot">Filter to apply to the pivot point. If false, point will not be paired at all, e.g. is white space.</param>
            <param name="filterFinal">Filter to apply to both the pivot and the paired point. If false, point will not be paired at all, e.g. pivot and paired point have same font.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.GroupIndexes(System.Int32[])">
            <summary>
            Group elements using Depth-first search.
            <para>https://en.wikipedia.org/wiki/Depth-first_search</para>
            </summary>
            <param name="edges">The graph. edges[i] = j indicates that there is an edge between i and j.</param>
            <returns>A List of HashSets containing the grouped indexes.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.GroupIndexes(System.Int32[][])">
            <summary>
            Group elements using Depth-first search.
            <para>https://en.wikipedia.org/wiki/Depth-first_search</para>
            </summary>
            <param name="edges">The graph. edges[i] = [j, k, l, ...] indicates that there is an edge between i and each element j, k, l, ...</param>
            <returns>A List of HashSets containing the grouped indexes.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Clustering.DfsIterative(System.Int32,System.Int32[][],System.Boolean[]@)">
            <summary>
            Depth-first search
            <para>https://en.wikipedia.org/wiki/Depth-first_search</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier">
            <summary>
            Algorithm that retrieve blocks that are labelled as decoration (e.g. headers, footers) for each page in the document, using a content and a geometric similarity measure.
            <para>Decoration blocks are blocks that contains information such as author names, publication titles, page numbers, etc.
            They are printed repeatedly at the border of each page, usually placed inside headers or footers, but sometimes also at the
            left or right edge of the page.</para>
            <para>See section 5.1 of 'Unsupervised document structure analysis of digital scientific articles' by S. Klampfl, M. Granitzer, K. Jack, R. Kern.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Page},UglyToad.PdfPig.Util.IWordExtractor,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter,System.Double,System.Int32,System.Int32)">
            <summary>
            Get blocks that are labelled as decoration for each page in the document, using a content and a geometric similarity measure.
            <para>Decoration blocks are blocks that contains information such as author names, publication titles, page numbers, etc.
            They are printed repeatedly at the border of each page, usually placed inside headers or footers, but sometimes also at the
            left or right edge of the page.</para>
            </summary>
            <param name="pages">The <see cref="T:UglyToad.PdfPig.Content.Page"/>s in the document. All of them are needed for the algorithm to work.</param>
            <param name="wordExtractor"></param>
            <param name="pageSegmenter"></param>
            <param name="similarityThreshold">Minimum similarity score to decide wether a block is labelled as decoration or not.</param>
            <param name="n">Number of blocks in a page to be considered when looking for decoration blocks.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Page},UglyToad.PdfPig.Util.IWordExtractor,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter,System.Func{System.String,System.String,System.Double},System.Double,System.Int32,System.Int32)">
            <summary>
            Get blocks that are labelled as decoration for each page in the document, using a content and a geometric similarity measure.
            <para>Decoration blocks are blocks that contains information such as author names, publication titles, page numbers, etc.
            They are printed repeatedly at the border of each page, usually placed inside headers or footers, but sometimes also at the
            left or right edge of the page.</para>
            </summary>
            <param name="pages">The <see cref="T:UglyToad.PdfPig.Content.Page"/>s in the document. All of them are needed for the algorithm to work.</param>
            <param name="wordExtractor"></param>
            <param name="pageSegmenter"></param>
            <param name="minimumEditDistanceNormalised">Minimum edit distance normalised. A value of 0 means both strings are exactly equal.</param>
            <param name="similarityThreshold">Minimum similarity score to decide wether a block is labelled as decoration or not.</param>
            <param name="n">Number of blocks in a page to be considered when looking for decoration blocks.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.Get(System.Collections.Generic.IReadOnlyList{System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock}},System.Double,System.Int32,System.Int32)">
            <summary>
            Get blocks that are labelled as decoration for each page in the document, using a content and a geometric similarity measure.
            <para>Decoration blocks are blocks that contains information such as author names, publication titles, page numbers, etc.
            They are printed repeatedly at the border of each page, usually placed inside headers or footers, but sometimes also at the
            left or right edge of the page.</para>
            </summary>
            <param name="pagesTextBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s of every pages in the document. All of them are needed for the algorithm to work.</param>
            <param name="similarityThreshold">Minimum similarity score to decide wether a block is labelled as decoration or not.</param>
            <param name="n">Number of blocks in a page to be considered when looking for decoration blocks.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.Get(System.Collections.Generic.IReadOnlyList{System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock}},System.Func{System.String,System.String,System.Double},System.Double,System.Int32,System.Int32)">
            <summary>
            Get blocks that are labelled as decoration for each page in the document, using a content and a geometric similarity measure.
            <para>Decoration blocks are blocks that contains information such as author names, publication titles, page numbers, etc.
            They are printed repeatedly at the border of each page, usually placed inside headers or footers, but sometimes also at the
            left or right edge of the page.</para>
            </summary>
            <param name="pagesTextBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s of every pages in the document. All of them are needed for the algorithm to work.</param>
            <param name="minimumEditDistanceNormalised">Minimum edit distance normalised. A value of 0 means both strings are exactly equal.</param>
            <param name="similarityThreshold">Minimum similarity score to decide wether a block is labelled as decoration or not.</param>
            <param name="n">Number of blocks in a page to be considered when looking for decoration blocks.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.ContentSimilarity(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Func{System.String,System.String,System.Double})">
            <summary>
            [The content similarity] is calculated from the normalized edit
            distance between the two content strings, where digits are replaced with “@” chars.
            A content similarity of 1 is reached when both strings are exactly equal.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.GeomSimilarity(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock)">
            <summary>
            The geometric similarity is the area of the intersection between the two boundingbox rectangles divided by the larger of the two boundingboxes.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.Similarity(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Func{System.String,System.String,System.Double})">
            <summary>
            This similarity score is a value in the range [0,1] and given
            by the product between the content and the geometric similarity.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.GetPreviousPageNumber(System.Int32,System.Int32)">
            <summary>
            If the document has more than three pages, we compare blocks on the next or previous page with an even or odd number,
            depending on whether the current page number is even or odd, to account for cases with a two-sided layout.
            </summary>
            <param name="currentPage">Current page number.</param>
            <param name="pagesCount">Total number of pages in the document.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DecorationTextBlockClassifier.GetNextPageNumber(System.Int32,System.Int32)">
            <summary>
            If the  document has more than three pages, we compare blocks on the next or previous page with an even or odd number,
            depending on whether the current page number is even or odd, to account for cases with a two-sided layout.
            </summary>
            <param name="currentPage">Current page number.</param>
            <param name="pagesCount">Total number of pages in the document.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances">
            <summary>
            Contains helpful tools for distance measures.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.Euclidean(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            The Euclidean distance is the "ordinary" straight-line distance between two points.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.WeightedEuclidean(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double,System.Double)">
            <summary>
            The weighted Euclidean distance.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
            <param name="wX">The weight of the X coordinates. Default is 1.</param>
            <param name="wY">The weight of the Y coordinates. Default is 1.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.Manhattan(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            The Manhattan distance between two points is the sum of the absolute differences of their Cartesian coordinates.
            <para>Also known as rectilinear distance, L1 distance, L1 norm, snake distance, city block distance, taxicab metric.</para>
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.Angle(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            The angle in degrees between the horizontal axis and the line between two points.
            <para>-180 ≤ θ ≤ 180</para>
            </summary>
            <param name="startPoint">The first point.</param>
            <param name="endPoint">The second point.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.Vertical(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            The absolute distance between the Y coordinates of two points.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.Horizontal(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            The absolute distance between the X coordinates of two points.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.BoundAngle180(System.Double)">
            <summary>
            Bound angle so that -180 ≤ θ ≤ 180.
            </summary>
            <param name="angle">The angle to bound.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.BoundAngle0to360(System.Double)">
            <summary>
            Bound angle so that 0 ≤ θ ≤ 360.
            </summary>
            <param name="angle">The angle to bound.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.MinimumEditDistance(System.String,System.String)">
            <summary>
            Get the minimum edit distance between two strings.
            </summary>
            <param name="string1">The first string.</param>
            <param name="string2">The second string.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.MinimumEditDistanceNormalised(System.String,System.String)">
            <summary>
            Get the minimum edit distance between two strings.
            <para>Returned values are between 0 and 1 included. A value of 0 means that the two strings are indentical.</para>
            </summary>
            <param name="string1">The first string.</param>
            <param name="string2">The second string.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.FindIndexNearest``1(``0,System.Collections.Generic.IReadOnlyList{``0},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{``0,UglyToad.PdfPig.Core.PdfPoint},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Double@)">
            <summary>
            Find the index of the nearest point, excluding itself.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="element">The reference point, for which to find the nearest neighbour.</param>
            <param name="candidates">The list of neighbours candidates.</param>
            <param name="pivotPoint"></param>
            <param name="candidatePoint"></param>
            <param name="distanceMeasure">The distance measure to use.</param>
            <param name="distance">The distance between the reference element and its nearest neighbour.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Distances.FindIndexNearest``1(``0,System.Collections.Generic.IReadOnlyList{``0},System.Func{``0,UglyToad.PdfPig.Core.PdfLine},System.Func{``0,UglyToad.PdfPig.Core.PdfLine},System.Func{UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.Core.PdfLine,System.Double},System.Double@)">
            <summary>
            Find the index of the nearest line, excluding itself.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="element">The reference line, for which to find the nearest neighbour.</param>
            <param name="candidates">The list of neighbours candidates.</param>
            <param name="pivotLine"></param>
            <param name="candidateLine"></param>
            <param name="distanceMeasure">The distance measure between two lines to use.</param>
            <param name="distance">The distance between the reference element and its nearest neighbour.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.DuplicateOverlappingTextProcessor">
            <summary>
            Checks if each letter is a duplicate and overlaps any other letter and remove the duplicate, and flag the remaining as bold.
            <para>Logic inspired from PdfBox's PDFTextStripper class.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.DuplicateOverlappingTextProcessor.Get(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Letter})">
            <summary>
            Checks if each letter is a duplicate and overlaps any other letter and remove the duplicate, and flag the remaining as bold.
            <para>Logic inspired from PdfBox's PDFTextStripper class.</para>
            </summary>
            <param name="letters">Letters to be processed.</param>
            <returns>Letters with no duplicate overlapping.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter">
            <summary>
            Alto 4.1 (XML) text exporter.
            <para>See https://github.com/altoxml/schema </para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.#ctor(UglyToad.PdfPig.Util.IWordExtractor,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter,System.Double,System.String)">
            <summary>
            Alto 4.1 (XML).
            <para>See https://github.com/altoxml/schema </para>
            </summary>
            <param name="wordExtractor">Extractor used to identify words in the document.</param>
            <param name="pageSegmenter">Segmenter used to split page into blocks.</param>
            <param name="scale">Scale multiplier to apply to output document, defaults to 1.</param>
            <param name="indent">Character to use for indentation, defaults to tab.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.Get(UglyToad.PdfPig.PdfDocument,System.Boolean)">
            <summary>
            Get the Alto (XML) string of the pages layout.
            </summary>
            <param name="document">The document to extract page layouts from.</param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.Get(UglyToad.PdfPig.Content.Page)">
            <summary>
            Get the Alto (XML) string of the page layout. Excludes <see cref="T:UglyToad.PdfPig.Geometry.PdfSubpath" />s.
            </summary>
            <param name="page">The page to export the XML layout for.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.Get(UglyToad.PdfPig.Content.Page,System.Boolean)">
            <summary>
            Get the Alto (XML) string of the page layout.
            </summary>
            <param name="page">The page to export the XML layout for.</param>
            <param name="includePaths">Whether the output should include the PdfPaths present in the page.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.CreateAltoDocument(System.String)">
            <summary>
            Create an empty <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.AltoXmlTextExporter.Deserialize(System.String)">
            <summary>
            Deserialize an <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument"/> from a given Alto format XML document.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument">
            <summary>
            [Alto] Alto Schema root
            <para>Version 4.1</para>
            See https://github.com/altoxml/schema
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoAlternative">
            <summary>
            [Alto] Alternative.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoAlternative.Purpose">
            <summary>
            Purpose.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoAlternative.Value">
            <summary>
            Value.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock">
            <summary>
            [Alto] Base type for any kind of block on the page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Shape">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.StyleRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.TagRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.ProcessingRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Rotation">
            <summary>
            The rotation of e.g. text or illustration within the block. The value is in degree counterclockwise.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.RotationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.IdNext">
            <summary>
            The next block in reading sequence on the page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.CorrectionStatus">
            <summary>
            Correction Status. Indicates whether manual correction has been done or not. 
            The correction status should be recorded at the highest level possible (Block, TextLine, String).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.CorrectionStatusSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Href">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Role">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Arcrole">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Title">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Show">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.ShowSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.Actuate">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlock.ActuateSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeActuate">
            <summary>
            [Alto/xlink] Block Type Actuate
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeActuate.OnLoad">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeActuate.OnRequest">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeActuate.Other">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeActuate.None">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow">
            <summary>
            [Alto/xlink] Block Type Show.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow.New">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow.Replace">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow.Embed">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow.Other">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoBlockTypeShow.None">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle">
            <summary>
            [Alto] A circle shape. <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle.HorizontalPosition"/> and <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle.VerticalPosition"/> describe the center of the circle.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle.HorizontalPosition">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle.VerticalPosition">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoCircle.Radius">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoComposedBlock">
            <summary>
            [Alto] A block that consists of other blocks.
            <para>WARNING: The CIRCULAR GROUP REFERENCES was removed from the xsd.
            NEED TO ADD IT BACK!!!</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoComposedBlock.TypeComposed">
            <summary>
            A user defined string to identify the type of composed block (e.g. table, advertisement, ...)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoComposedBlock.FileId">
            <summary>
            An ID to link to an image which contains only the composed block. 
            The ID and the file link is defined in the related METS file.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescription">
            <summary>
            [Alto] Description
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescription.MeasurementUnit">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescription.SourceImageInformation">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescription.OcrProcessing">
            <summary>
            Element deprecated. 'Processing' should be used instead.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescription.Processings">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescriptionOcrProcessing">
            <inheritdoc />
            <summary>
            [Alto] Description Ocr Processing
            <para>Element deprecated. 'AltoProcessing' should be used instead.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescriptionOcrProcessing.Id">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescriptionProcessing">
            <inheritdoc />
            <summary>
            [Alto] Description Processing
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDescriptionProcessing.Id">
            <summary>
            Id.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDocumentIdentifier">
            <summary>
            [Alto] A unique identifier for the document.
            <para>This identifier must be unique within the local  
            To facilitate file sharing or interoperability with other systems, 
            documentIdentifierLocation may be added to designate the system or 
            application where the identifier is unique.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDocumentIdentifier.DocumentIdentifierLocation">
            <summary>
            A location qualifier, i.e., a namespace.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoDocumentIdentifier.Value">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse">
            <summary>
            [Alto] An ellipse shape. HPOS and VPOS describe the center of the ellipse.
            HLENGTH and VLENGTH are the width and height of the described ellipse.
            <para>The attribute ROTATION tells the rotation of the e.g. text or 
            illustration within the block.The value is in degrees counterclockwise.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.HorizontalPosition">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.VerticalPosition">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.HorizontalLength">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.VerticalLength">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.Rotation">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoEllipse.RotationSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFileIdentifier">
            <summary>
            [Alto] A unique identifier for the image file. This is drawn from MIX.
            
            <para>This identifier must be unique within the local  
            To facilitate file sharing or interoperability with other systems, 
            fileIdentifierLocation may be added to designate the system or 
            application where the identifier is unique.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFileIdentifier.FileIdentifierLocation">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFileIdentifier.Value">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles">
            <summary>
            [Alto] Font styles.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.Bold">
            <summary>
            Bold.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.Italics">
            <summary>
            Italics.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.Subscript">
            <summary>
            Subscript.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.Superscript">
            <summary>
            Superscript.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.SmallCaps">
            <summary>
            Small caps.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontStyles.Underline">
            <summary>
            Underline.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontType">
            <summary>
            [Alto] Font type (Serif or Sans-Serif).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontType.Serif">
            <summary>
            Serif.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontType.SansSerif">
            <summary>
            Sans-serif.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontWidth">
            <summary>
            [Alto] Font width (Fixed or proportional).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontWidth.Proportional">
            <summary>
            Proportional.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoFontWidth.Fixed">
            <summary>
            Remarks.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph">
            <summary>
            [Alto] Modern OCR software stores information on glyph level. A glyph is essentially a character or ligature.
            Accordingly the value for the glyph element will be defined as follows:
            Pre-composed representation = base + combining character(s) (decomposed representation)
            See http://www.fileformat.info/info/unicode/char/0101/index.htm
            "U+0101" = (U+0061) + (U+0304)
            "combining characters" ("base characters" in combination with non-spacing marks or characters which are combined to one) are represented as one "glyph", e.g.áàâ.
            
            <para>Each glyph has its own coordinate information and must be separately addressable as a distinct object.
            Correction and verification processes can be carried out for individual characters.</para>
            
            <para>Post-OCR analysis of the text as well as adaptive OCR algorithm must be able to record information on glyph level.
            In order to reproduce the decision of the OCR software, optional characters must be recorded.These are called variants.
            The OCR software evaluates each variant and picks the one with the highest confidence score as the glyph.
            The confidence score expresses how confident the OCR software is that a single glyph had been recognized correctly.</para>
            
            <para>The glyph elements are in order of the word. Each glyph need to be recorded to built up the whole word sequence.</para>
            
            <para>The glyph’s CONTENT attribute is no replacement for the string’s CONTENT attribute.
            Due to post-processing steps such as correction the values of both attributes may be inconsistent.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.Shape">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.Variant">
            <summary>
            Alternative (combined) character for the glyph, outlined by OCR engine or similar recognition processes.
            In case the variant are two (combining) characters, two characters are outlined in one Variant element.
            E.g. a Glyph element with CONTENT="m" can have a Variant element with the content "rn".
            <para>Details for different use-cases see on the samples on GitHub.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.Content">
            <summary>
            CONTENT contains the precomposed representation (combining character) of the character from the parent String element.
            The sequence position of the Gylph element matches the position of the character in the String.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.Gc">
            <summary>
            This GC attribute records a float value between 0.0 and 1.0 that expresses the level of confidence for the variant where is 1 is certain.
            This attribute is optional. If it is not available, the default value for the variant is "0".
            
            <para>The GC attribute semantic is the same as the WC attribute on the String element and VC on Variant element.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.GcSpecified">
            <remarks/>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGlyph.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoGraphicalElement">
            <summary>
            [Alto] A graphic used to separate blocks. Usually a line or rectangle.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoIllustration">
            <summary>
            [Alto] A picture or image.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoIllustration.IllustrationType">
            <summary>
            A user defined string to identify the type of illustration like photo, map, drawing, chart, ...
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoIllustration.FileId">
            <summary>
            A link to an image which contains only the illustration.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice">
            <summary>
            [Alto] There are following variation of tag types available:
            LayoutTag – criteria about arrangement or graphical appearance; 
            StructureTag – criteria about grouping or formation; 
            RoleTag – criteria about function or mission; 
            NamedEntityTag – criteria about assignment of terms to their relationship / meaning (NER); 
            OtherTag – criteria about any other characteristic not listed above, the TYPE attribute is intended to be used for classification within those.; 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice.LayoutTag">
            <summary>
            Criteria about arrangement or graphical appearance.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice.NamedEntityTag">
            <summary>
            Criteria about assignment of terms to their relationship / meaning (NER).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice.OtherTag">
            <summary>
            Criteria about any other characteristic not listed above, the TYPE attribute is intended to be used for classification within those.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice.RoleTag">
            <summary>
            Criteria about function or mission.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoItemsChoice.StructureTag">
            <summary>
            Criteria about grouping or formation.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoLayout">
            <summary>
            [Alto] Layout.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoLayout.Pages">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoLayout.StyleRefs">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoMeasurementUnit">
            <summary>
            [Alto] All measurement values inside the alto file are related to this unit, except the font size.
            
            Coordinates as being used in HPOS and VPOS are absolute coordinates referring to the upper-left corner of a page.
            The upper left corner of the page is defined as coordinate (0/0). 
            
            <para>values meaning:
            mm10: 1/10th of millimeter; 
            inch1200: 1/1200th of inch; 
            pixel: 1 pixel</para>
            
            The values for pixel will be related to the resolution of the image based
            on which the layout is described. Incase the original image is not known
            the scaling factor can be calculated based on total width and height of
            the image and the according information of the PAGE element.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoMeasurementUnit.Pixel">
            <summary>
            1 pixel.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoMeasurementUnit.Mm10">
            <summary>
            1/10th of millimeter.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoMeasurementUnit.Inch1200">
            <summary>
            1/1200th of inch.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoOcrProcessing">
            <summary>
            [Alto] Ocr Processing
            <para>Element deprecated. 'AltoProcessing' should be used instead.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoOcrProcessing.PreProcessingStep">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoOcrProcessing.OcrProcessingStep">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoOcrProcessing.PostProcessingStep">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage">
            <summary>
            [Alto] One page of a document.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.TopMargin">
            <summary>
            The area between the top line of print and the upper edge of the leaf. It may contain page number or running title.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.LeftMargin">
            <summary>
            The area between the printspace and the left border of a page. May contain margin notes.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.RightMargin">
            <summary>
            The area between the printspace and the right border of a page. May contain margin notes.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.BottomMargin">
            <summary>
            The area between the bottom line of letterpress or writing and the bottom edge of the leaf.
            It may contain a page number, a signature number or a catch word.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PrintSpace">
            <summary>
            Rectangle covering the printed area of a page. Page number and running title are not part of the print space.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PageClass">
            <summary>
            Any user-defined class like title page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.StyleRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.ProcessingRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Height">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.HeightSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Width">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.WidthSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PhysicalImgNr">
            <summary>
            The number of the page within the document.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PrintedImgNr">
            <summary>
            The page number that is printed on the page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Quality">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.QualitySpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.QualityDetail">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Position">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PositionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Processing">
            <summary>
            A link to the processing description that has been used for this page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Accuracy">
            <summary>
            Estimated percentage of OCR Accuracy in range from 0 to 100
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.AccuracySpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.Pc">
            <summary>
            
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPage.PcSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace">
            <inheritdoc />
            <summary>
            [Alto] A region on a page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.Shape">
            <summary>
            Shape.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.TextBlock">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.Illustrations">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.GraphicalElements">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.ComposedBlocks">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.StyleRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPageSpace.ProcessingRefs">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle">
            <summary>
            [Alto] A paragraph style defines formatting properties of text blocks.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.Align">
            <summary>
            Indicates the alignement of the paragraph. Could be left, right, center or justify.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.AlignSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.Left">
            <summary>
            Left indent of the paragraph in relation to the column.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.LeftSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.Right">
            <summary>
            Right indent of the paragraph in relation to the column.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.RightSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.LineSpace">
            <summary>
            Line spacing between two lines of the paragraph. Measurement calculated from baseline to baseline.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.LineSpaceSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.FirstLine">
            <summary>
            Indent of the first line of the paragraph if this is different from the other lines. A negative 
            value indicates an indent to the left, a positive value indicates an indent to the right.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyle.FirstLineSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyleAlign">
            <summary>
            [Alto] Indicates the alignment of the paragraph. Could be left, right, center or justify.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyleAlign.Left">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyleAlign.Right">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyleAlign.Center">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoParagraphStyleAlign.Block">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPolygon">
            <summary>
            [Alto] A polygon shape.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPolygon.Points">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition">
            <summary>
            [Alto] Position of the page. Could be lefthanded, righthanded, cover, foldout or single if it has no special position.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition.Left">
            <summary>
            Left page.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition.Right">
            <summary>
            Right page.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition.Foldout">
            <summary>
            Foldout page.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition.Single">
            <summary>
            Single page.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPosition.Cover">
            <summary>
            Cover page.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement">
            <summary>
            Encapsulates width/height and position data.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.Height">
            <summary>
            Height.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.HeightSpecified">
            <summary>
            Whether to include <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.Height"/> in the output.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.Width">
            <summary>
            Width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.WidthSpecified">
            <summary>
            Whether to include <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.Width"/> in the output.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.HorizontalPosition">
            <summary>
            Horizontal position.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.HorizontalPositionSpecified">
            <summary>
            Whether to include <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.HorizontalPosition"/> in the output.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.VerticalPosition">
            <summary>
            Vertical position.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.VerticalPositionSpecified">
            <summary>
            Whether to include <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoPositionedElement.VerticalPosition"/> in the output.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory">
            <summary>
            [Alto] Classification of the category of operation, how the file was created, including generation, modification, 
            preprocessing, postprocessing or any other steps.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory.ContentGeneration">
            <summary>
            Content generation.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory.ContentModification">
            <summary>
            Content modification.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory.PreOperation">
            <summary>
            Pre-operation.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory.PostOperation">
            <summary>
            Post-operation.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingCategory.Other">
            <summary>
            Other.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingSoftware">
            <summary>
            [Alto] Information about a software application. Where applicable, the preferred method
            for determining this information is by selecting Help -- About.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingSoftware.SoftwareCreator">
            <summary>
            The name of the organization or company that created the application.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingSoftware.SoftwareName">
            <summary>
            The name of the application.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingSoftware.SoftwareVersion">
            <summary>
            The version of the application.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingSoftware.ApplicationDescription">
            <summary>
            A description of any important characteristics of the application, especially for
            non-commercial applications. For example, if a non-commercial application is built
            using commercial components, e.g., an OCR engine SDK. Those components should be mentioned here.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep">
            <summary>
            [Alto] Description of the processing step.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingCategory">
            <summary>
            Classification of the category of operation, how the file was created, including 
            generation, modification, preprocessing, postprocessing or any other steps.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingCategorySpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingDateTime">
            <summary>
            Date or DateTime the image was processed.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingAgency">
            <summary>
            Identifies the organization level producer(s) of the processed image.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingStepDescription">
            <summary>
            An ordinal listing of the image processing steps performed. For example, "image despeckling."
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingStepSettings">
            <summary>
            A description of any setting of the processing application. For example, for a multi-engine
            OCR application this might include the engines which were used. Ideally, this description 
            should be adequate so that someone else using the same application can produce identical results.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoProcessingStep.ProcessingSoftware">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality">
            <summary>
            [Alto] Gives brief information about original page quality
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.OK">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.Missing">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.MissingInOriginal">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.Damaged">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.Retained">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.Target">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoQuality.AsInOriginal">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoShape">
            <summary>
            [Alto] Describes the bounding shape of a block, if it is not rectangular.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoShape.Item">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSourceImageInformation">
            <summary>
            [Alto] Information to identify the image file from which the OCR text was created.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSourceImageInformation.FileName">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSourceImageInformation.FileIdentifiers">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSourceImageInformation.DocumentIdentifiers">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSP">
            <summary>
            [Alto] A white space.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSP.Id">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString">
            <summary>
            [Alto] A sequence of chars. Strings are separated by white spaces or hyphenation chars.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Shape">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Alternative">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Glyph">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.StyleRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.TagRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.ProcessingRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Content">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Style">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.StyleSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.SubsType">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.SubsTypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.SubsContent">
            <summary>
            Content of the substitution.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Wc">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.WcSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Cc">
            <summary>
            Confidence level of each character in that string. A list of numbers,
            one number between 0 (sure) and 9 (unsure) for each character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.CorrectionStatus">
            <summary>
            Correction Status. Indicates whether manual correction has been done or not. 
            The correction status should be recorded at the highest level possible (Block, TextLine, String).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.CorrectionStatusSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.Language">
            <summary>
            Attribute to record language of the string. The language should be recorded at the highest level possible.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoString.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoStyles">
            <summary>
            [Alto] Styles.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoStyles.TextStyle">
            <summary>
            Text Style.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoStyles.ParagraphStyle">
            <summary>
            Paragraph Style.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSubsType">
            <summary>
            [Alto] Type of the substitution (if any).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSubsType.HypPart1">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSubsType.HypPart2">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoSubsType.Abbreviation">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag">
            <summary>
            [Alto] Tag.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.XmlData">
            <summary>
            The xml data wrapper element XmlData is used to contain XML encoded metadata.
            The content of an XmlData element can be in any namespace or in no namespace.
            As permitted by the XML Schema Standard, the processContents attribute value for the
            metadata in an XmlData is set to "lax". Therefore, if the source schema and its location are
            identified by means of an XML schemaLocation attribute, then an XML processor will validate
            the elements for which it can find declarations.If a source schema is not identified, or cannot be
            found at the specified schemaLocation, then an XML validator will check for well-formedness,
            but otherwise skip over the elements appearing in the XmlData element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.Type">
            <summary>
            Type can be used to classify and group the information within each tag element type.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.Label">
            <summary>
            Content / information value of the tag.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.Description">
            <summary>
            Description text for tag information for clarification.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTag.Uri">
            <summary>
            Any URI for authority or description relevant information.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTags">
            <summary>
            [Alto] There are following variation of tag types available:
            LayoutTag – criteria about arrangement or graphical appearance; 
            StructureTag – criteria about grouping or formation; 
            RoleTag – criteria about function or mission; 
            NamedEntityTag – criteria about assignment of terms to their relationship / meaning (NER); 
            OtherTag – criteria about any other characteristic not listed above, the TYPE attribute is intended to be used for classification within those.;
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTags.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTags.ItemsElementName">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTagXmlData">
            <summary>
            [Alto] The xml data wrapper element XmlData is used to contain XML encoded metadata.
            The content of an XmlData element can be in any namespace or in no namespace.
            As permitted by the XML Schema Standard, the processContents attribute value for the
            metadata in an XmlData is set to "lax". Therefore, if the source schema and its location are
            identified by means of an XML schemaLocation attribute, then an XML processor will validate
            the elements for which it can find declarations. If a source schema is not identified, or cannot be
            found at the specified schemaLocation, then an XML validator will check for well-formedness,
            but otherwise skip over the elements appearing in the XmlData element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTagXmlData.Any">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlock">
            <inheritdoc />
            <summary>
            [Alto] A block of text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlock.TextLines">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlock.Language">
            <summary>
            Attribute deprecated. LANG should be used instead.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlock.Lang">
            <summary>
            Attribute to record language of the textblock.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlock.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine">
            <summary>
            [Alto] A single line of text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Shape">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Strings">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Sp">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Hyp">
            <summary>
            A hyphenation char. Can appear only at the end of a line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.StyleRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.TagRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.ProcessingRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.BaseLine">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.BaseLineSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.Language">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.CorrectionStatus">
            <summary>
            Correction Status. Indicates whether manual correction has been done or not. 
            The correction status should be recorded at the highest level possible (Block, TextLine, String).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.CorrectionStatusSpecified">
            <remarks/>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLine.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLineHyp">
            <summary>
            [Alto] A hyphenation char. Can appear only at the end of a line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextBlockTextLineHyp.Content">
            <summary>
            Content.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle">
            <summary>
            [Alto] A text style defines font properties of text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontFamily">
            <summary>
            The font name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontType">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontTypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontWidth">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontWidthSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontSize">
            <summary>
            The font size, in points (1/72 of an inch).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontColor">
            <summary>
            The font color as an RGB value.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontStyle">
            <summary>
            The font style.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoTextStyle.FontStyleSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoVariant">
            <summary>
            [Alto] Alternative (combined) character for the glyph, outlined by OCR engine or similar recognition processes.
            In case the variant are two (combining) characters, two characters are outlined in one Variant element.
            E.g. a Glyph element with CONTENT="m" can have a Variant element with the content "rn".
            Details for different use-cases see on the samples on GitHub.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoVariant.Content">
            <summary>
            Each Variant represents an option for the glyph that the OCR software detected as possible alternatives.
            In case the variant are two(combining) characters, two characters are outlined in one Variant element.
            E.g.a Glyph element with CONTENT="m" can have a Variant element with the content "rn".
            
            <para>Details for different use-cases see on the samples on GitHub.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoVariant.Vc">
            <summary>
            This VC attribute records a float value between 0.0 and 1.0 that expresses the level of confidence 
            for the variant where is 1 is certain.
            This attribute is optional. If it is not available, the default value for the variant is "0".
            The VC attribute semantic is the same as the GC attribute on the Glyph element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.AltoVariant.VcSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.Description">
            <summary>
            Describes general settings of the alto file like measurement units and metadata
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.Styles">
            <summary>
            Styles define properties of layout elements. A style defined in a parent element
            is used as default style for all related children elements.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.Tags">
            <summary>
            Tag define properties of additional characteristic. The tags are referenced from 
            related content element on Block or String element by attribute TAGREF via the tag ID.
            
            This container element contains the individual elements for LayoutTags, StructureTags,
            RoleTags, NamedEntityTags and OtherTags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.Layout">
            <summary>
            The root layout element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.Alto.AltoDocument.SchemaVersion">
            <summary>
            Schema version of the ALTO file.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter">
            <summary>
            hOCR v1.2 (HTML) text exporter.
            <para>See http://kba.cloud/hocr-spec/1.2/ </para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.#ctor(UglyToad.PdfPig.Util.IWordExtractor,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter,System.Double,System.String)">
            <summary>
            hOCR v1.2 (HTML)
            <para>See http://kba.cloud/hocr-spec/1.2/ </para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.Get(UglyToad.PdfPig.PdfDocument,System.Boolean,System.Boolean)">
            <summary>
            Get the hOCR (HTML) string of the page layout.
            </summary>
            <param name="document">The document.</param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
            <param name="useHocrjs">Will add a reference to the 'hocrjs' script just before the closing 'body' tag, adding the
            interface to a plain hOCR file.<para>See https://github.com/kba/hocrjs for more information.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.Get(UglyToad.PdfPig.Content.Page)">
            <summary>
            Get the hOCR (HTML) string of the page layout. Excludes PdfPaths.
            </summary>
            <param name="page">The page.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.Get(UglyToad.PdfPig.Content.Page,System.Boolean,System.String,System.Boolean)">
            <summary>
            Get the hOCR (HTML) string of the page layout.
            </summary>
            <param name="page">The page.</param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
            <param name="imageName">The image name, if any.</param>
            <param name="useHocrjs">Will add a reference to the 'hocrjs' script just before the closing 'body' tag, adding the interface to a plain hOCR file.<para>See https://github.com/kba/hocrjs for more information.</para></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetIndent(System.Int32)">
            <summary>
            Get indent string from level.
            </summary>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCode(UglyToad.PdfPig.Content.Page,System.Boolean,System.String)">
            <summary>
            Get the hOCR string for the page.
            <para>http://kba.cloud/hocr-spec/1.2/#elementdef-ocr_page</para>
            </summary>
            <param name="page"></param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
            <param name="imageName"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCode(UglyToad.PdfPig.Graphics.PdfPath,System.Double,System.Boolean,System.Int32)">
            <summary>
            Get the hOCR string for the path.
            <para>http://kba.cloud/hocr-spec/1.2/#elementdef-ocr_linedrawing</para>
            </summary>
            <param name="path"></param>
            <param name="pageHeight"></param>
            <param name="subPaths"></param>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCodeArea(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double,System.Int32)">
            <summary>
            Get the hOCR string for the area.
            <para>http://kba.cloud/hocr-spec/1.2/#elementdef-ocr_carea</para>
            </summary>
            <param name="block">The text area.</param>
            <param name="pageHeight"></param>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCodeParagraph(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double,System.Int32)">
            <summary>
            Get the hOCR string for the paragraph.
            <para>See http://kba.cloud/hocr-spec/1.2/#elementdef-ocr_par</para>
            </summary>
            <param name="block">The paragraph.</param>
            <param name="pageHeight"></param>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCode(UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine,System.Double,System.Int32)">
            <summary>
            Get the hOCR string for the text line.
            <para>See http://kba.cloud/hocr-spec/1.2/#elementdef-ocr_line</para>
            </summary>
            <param name="line"></param>
            <param name="pageHeight"></param>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCode(UglyToad.PdfPig.Content.Word,System.Double,System.Int32)">
            <summary>
            Get the hOCR string for the word.
            <para>See http://kba.cloud/hocr-spec/1.2/#elementdef-ocrx_word</para>
            </summary>
            <param name="word"></param>
            <param name="pageHeight"></param>
            <param name="level">The indent level.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.HOcrTextExporter.GetCode(UglyToad.PdfPig.Core.PdfRectangle,System.Double)">
            <summary>
            Get the hOCR string for the bounding box.
            <para>See http://kba.cloud/hocr-spec/1.2/#propdef-bbox</para>
            </summary>
            <param name="rectangle"></param>
            <param name="pageHeight"></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.ITextExporter">
            <summary>
            Exports the page's text into the desired format.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.ITextExporter.Get(UglyToad.PdfPig.Content.Page)">
            <summary>
            Get the text representation of a page in a desired format.
            </summary>
            <param name="page">The page to convert to the format.</param>
            <returns>The <see langword="string"/> containing the page contents represented in a compatible format.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter">
            <summary>
            PAGE-XML 2019-07-15 (XML) text exporter.
            <para>See https://github.com/PRImA-Research-Lab/PAGE-XML </para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.#ctor(UglyToad.PdfPig.Util.IWordExtractor,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter,UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.IReadingOrderDetector,System.Double,System.String)">
            <summary>
            PAGE-XML 2019-07-15 (XML) text exporter.
            <para>See https://github.com/PRImA-Research-Lab/PAGE-XML </para>
            </summary>
            <param name="wordExtractor"></param>
            <param name="pageSegmenter"></param>
            <param name="readingOrderDetector"></param>
            <param name="scale"></param>
            <param name="indent">Indent character.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.Get(UglyToad.PdfPig.PdfDocument,System.Boolean)">
            <summary>
            Get the PAGE-XML (XML) string of the pages layout.
            </summary>
            <param name="document"></param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.Get(UglyToad.PdfPig.Content.Page)">
            <summary>
            Get the PAGE-XML (XML) string of the pages layout. Excludes PdfPaths.
            </summary>
            <param name="page"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.Get(UglyToad.PdfPig.Content.Page,System.Boolean)">
            <summary>
            Get the PAGE-XML (XML) string of the pages layout.
            </summary>
            <param name="page"></param>
            <param name="includePaths">Draw PdfPaths present in the page.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.PointToString(UglyToad.PdfPig.Core.PdfPoint,System.Double,System.Double,System.Double)">
            <summary>
            Converts a point to a string
            </summary>
            <param name="point"></param>
            <param name="pageWidth">The width of the page where the pdf point is located on</param>
            <param name="pageHeight">The height of the page where the pdf point is located on</param>
            <param name="scaleToApply"></param>
            <returns></returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.ToRgbEncoded(UglyToad.PdfPig.Graphics.Colors.IColor)">
            <summary>
            PageXml Text colour in RGB encoded format
            <para>(red value) + (256 x green value) + (65536 x blue value).</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PageXmlTextExporter.Deserialize(System.String)">
            <summary>
            Deserialize an <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument"/> from a given PAGE format XML document.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument">
            <summary>
            PAGE (Page Analysis and Ground-Truth Elements) root
            <para>Version 2019-07-15</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.Metadata">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.Page">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PcGtsId">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAdvertRegion">
            <summary>
            Regions containing advertisements.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAdvertRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAdvertRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAdvertRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAdvertRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlignSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlignSimpleType.Left">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlignSimpleType.Centre">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlignSimpleType.Right">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlignSimpleType.Justify">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlternativeImage">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlternativeImage.FileName">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlternativeImage.Comments">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlternativeImage.Conf">
            <summary>
            Confidence value (between 0 and 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlAlternativeImage.ConfSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBaseline">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBaseline.Points">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBaseline.Conf">
            <summary>
            Confidence value (between 0 and 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBaseline.ConfSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBorder">
            <summary>
            Border of the actual page (if the scanned image
            contains parts not belonging to the page).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlBorder.Coords">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion">
            <summary>
            Regions containing charts or graphs of any type, should be marked as chart regions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.Type">
            <summary>
            The type of chart in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.NumColours">
            <summary>
            An approximation of the number of colours used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.NumColoursSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.EmbText">
            <summary>
            Specifies whether the region also contains text
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartRegion.EmbTextSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Bar">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Line">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Pie">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Scatter">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Surface">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChartSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChemRegion">
            <summary>
            Regions containing chemical formulas.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChemRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a
            region has to be rotated in clockwise
            direction in order to correct the present
            skew(negative values indicate
            anti-clockwise rotation). Range:
            -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChemRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChemRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlChemRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourDepthSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourDepthSimpleType.BiLevel">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourDepthSimpleType.GreyScale">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourDepthSimpleType.Colour">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourDepthSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Black">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Blue">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Brown">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Cyan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Green">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Grey">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Indigo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Magenta">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Orange">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Pink">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Red">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Turquoise">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Violet">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.White">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Yellow">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlColourSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCoords">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCoords.Points">
            <summary>
            Polygon outline of the element as a path of points.
            No points may lie outside the outline of its parent,
            which in the case of Border is the bounding rectangle
            of the root image. Paths are closed by convention,
            i.e.the last point logically connects with the first
            (and at least 3 points are required to span an area).
            Paths must be planar (i.e.must not self-intersect).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCoords.Conf">
            <summary>
            Confidence value (between 0 and 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCoords.ConfSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCustomRegion">
            <summary>
            Regions containing content that is not covered
            by the default types(text, graphic, image,
            line drawing, chart, table, separator, maths,
            map, music, chem, advert, noise, unknown).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlCustomRegion.Type">
            <summary>
            Information on the type of content represented by this region
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.AlternativeImages">
            <summary>
            Alternative glyph images (e.g. black-and-white)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Coords">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Graphemes">
            <summary>
            Container for graphemes, grapheme groups and non-printing characters
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.TextEquivs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.TextStyle">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Ligature">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.LigatureSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Symbol">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.SymbolSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Script">
            <summary>
            The script used for the glyph
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.ScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Production">
            <summary>
            Overrides the production attribute of the parent word / text line / text region.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.ProductionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGlyph.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGrapheme">
            <summary>
            Represents a sub-element of a glyph. Smallest graphical unit that can be assigned a Unicode code point.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGrapheme.Coords">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase">
            <summary>
            Base type for graphemes, grapheme groups and non-printing characters.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.TextEquivs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.Index">
            <summary>
            Order index of grapheme, group, or non-printing character
            within the parent container (graphemes or glyph or grapheme group).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.Ligature">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.LigatureSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.CharType">
            <summary>
            Type of character represented by the grapheme, group, or non-printing character element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.CharTypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBase.Comments">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBaseCharType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBaseCharType.Base">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeBaseCharType.Combining">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeGroup">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphemeGroup.Items">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion">
            <summary>
            Regions containing simple graphics, such as a company
            logo, should be marked as graphic regions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.Type">
            <summary>
            The type of graphic in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.NumColours">
            <summary>
            An approximation of the number of colours used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.NumColoursSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.EmbText">
            <summary>
            Specifies whether the region also contains text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicRegion.EmbTextSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Logo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Letterhead">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Decoration">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Frame">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.HandwrittenAnnotation">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Stamp">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Signature">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Barcode">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.PaperGrow">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.PunchHole">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGraphicsSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGridPoints">
            <summary>
            Points with x,y coordinates.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGridPoints.Index">
            <summary>
            The grid row index
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGridPoints.Points">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.Paragraph">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.List">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.ListItem">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.Figure">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.Article">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.Div">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlGroupSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion">
            <summary>
            An image is considered to be more intricate and complex than a graphic. These can be photos or drawings.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.ColourDepth">
            <summary>
            The colour bit depth required for the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.ColourDepthSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.EmbText">
            <summary>
            Specifies whether the region also contains text
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlImageRegion.EmbTextSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabel">
            <summary>
            Semantic label
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabel.Value">
            <summary>
            The label / tag (e.g. 'person'). Can be an RDF resource identifier (e.g. object of an RDF triple).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabel.Type">
            <summary>
            Additional information on the label (e.g. 'YYYY-mm-dd' for a date label). Can be used as predicate of an RDF triple.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabel.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels.Labels">
            <summary>
            A semantic label / tag
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels.ExternalModel">
            <summary>
            Reference to external model / ontology / schema
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels.ExternalId">
            <summary>
            E.g. an RDF resource identifier (to be used as subject or object of an RDF triple)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels.Prefix">
            <summary>
            Prefix for all labels (e.g. first part of an URI)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLabels.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType">
            <summary>
            ISO 639.x 2016-07-14
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Abkhaz">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Afar">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Afrikaans">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Akan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Albanian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Amharic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Arabic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Aragonese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Armenian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Assamese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Avaric">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Avestan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Aymara">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Azerbaijani">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bambara">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bashkir">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Basque">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Belarusian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bengali">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bihari">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bislama">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bosnian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Breton">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Bulgarian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Burmese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Cambodian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Cantonese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Catalan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Chamorro">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Chechen">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Chichewa">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Chinese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Chuvash">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Cornish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Corsican">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Cree">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Croatian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Czech">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Danish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Divehi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Dutch">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Dzongkha">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.English">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Esperanto">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Estonian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ewe">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Faroese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Fijian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Finnish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.French">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Fula">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Gaelic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Galician">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ganda">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Georgian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.German">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Greek">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Guaraní">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Gujarati">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Haitian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Hausa">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Hebrew">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Herero">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Hindi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.HiriMotu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Hungarian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Icelandic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ido">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Igbo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Indonesian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Interlingua">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Interlingue">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Inuktitut">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Inupiaq">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Irish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Italian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Japanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Javanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kalaallisut">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kannada">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kanuri">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kashmiri">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kazakh">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Khmer">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kikuyu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kinyarwanda">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kirundi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Komi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kongo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Korean">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kurdish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kwanyama">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Kyrgyz">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Lao">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Latin">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Latvian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Limburgish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Lingala">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Lithuanian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.LubaKatanga">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Luxembourgish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Macedonian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Malagasy">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Malay">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Malayalam">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Maltese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Manx">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Māori">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Marathi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Marshallese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Mongolian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Nauru">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Navajo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ndonga">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Nepali">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.NorthNdebele">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.NorthernSami">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Norwegian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.NorwegianBokmål">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.NorwegianNynorsk">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Nuosu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Occitan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ojibwe">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.OldChurchSlavonic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Oriya">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Oromo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ossetian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Pāli">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Panjabi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Pashto">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Persian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Polish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Portuguese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Punjabi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Quechua">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Romanian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Romansh">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Russian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Samoan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sango">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sanskrit">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sardinian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Serbian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Shona">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sindhi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sinhala">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Slovak">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Slovene">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Somali">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.SouthNdebele">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.SouthernSotho">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Spanish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Sundanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Swahili">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Swati">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Swedish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tagalog">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tahitian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tajik">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tamil">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tatar">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Telugu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Thai">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tibetan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tigrinya">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tonga">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tsonga">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Tswana">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Turkish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Turkmen">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Twi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Uighur">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Ukrainian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Urdu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Uzbek">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Venda">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Vietnamese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Volapük">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Walloon">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Welsh">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.WesternFrisian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Wolof">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Xhosa">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Yiddish">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Yoruba">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Zhuang">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Zulu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLanguageSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayer">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayer.RegionRefs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayer.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayer.ZIndex">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayer.Caption">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayers">
            <summary>
            Can be used to express the z-index of overlapping
            regions.An element with a greater z-index is always in
            front of another element with lower z-index.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLayers.Layers">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion">
            <summary>
            A line drawing is a single colour illustration without solid areas.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.PenColour">
            <summary>
            The pen (foreground) colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.PenColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.EmbText">
            <summary>
            Specifies whether the region also contains text
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlLineDrawingRegion.EmbTextSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMapRegion">
            <summary>
            Regions containing maps.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMapRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a
            region has to be rotated in clockwise
            direction in order to correct the present
            skew(negative values indicate
            anti-clockwise rotation). Range:
            -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMapRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMathsRegion">
            <summary>
            Regions containing equations and mathematical symbols should be marked as maths regions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMathsRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMathsRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMathsRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMathsRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.Creator">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.Created">
            <summary>
            The timestamp has to be in UTC (Coordinated Universal Time) and not local time.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.LastChange">
            <summary>
            The timestamp has to be in UTC (Coordinated Universal Time) and not local time.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.Comments">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.MetadataItems">
            <summary>
            
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadata.ExternalRef">
            <summary>
            External reference of any kind
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.Type">
            <summary>
            Type of metadata (e.g. author)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.Name">
            <summary>
            E.g. imagePhotometricInterpretation
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.Value">
            <summary>
            E.g. RGB
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.Date">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItem.DateSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItemType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItemType.Author">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItemType.ImageProperties">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItemType.ProcessingStep">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMetadataItemType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMusicRegion">
            <summary>
            Regions containing musical notations.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMusicRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMusicRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMusicRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlMusicRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlNoiseRegion">
            <summary>
            Noise regions are regions where no real data lies, only
            false data created by artifacts on the document or
            scanner noise.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlNonPrintingChar">
            <summary>
            A glyph component without visual representation 
            but with Unicode code point.
            Non-visual / non-printing / control character.
            Part of grapheme container (of glyph) or grapheme sub group.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup">
            <summary>
            Numbered group (contains ordered elements)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.regionRef">
            <summary>
            Optional link to a parent region of nested regions.
            The parent region doubles as reading order group.
            Only the nested regions should be allowed as group members.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Caption">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Continuation">
            <summary>
            Is this group a continuation of another group
            (from previous column or page, for example)?
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.ContinuationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroup.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Labels">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.RegionRef">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Index">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Caption">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Continuation">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.ContinuationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Custom">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlOrderedGroupIndexed.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.AlternativeImage">
            <summary>
            Alternative document page images (e.g.black-and-white).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Border">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.PrintSpace">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ReadingOrder">
            <summary>
            Order of blocks within the page.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Layers">
            <summary>
            Unassigned regions are considered to be in the (virtual) default layer which is to be treated as below any other layers.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Relations">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.TextStyle">
            <summary>
            Default text style
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageFilename">
            <summary>
            Contains the image file name including the file extension.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageWidth">
            <summary>
            Specifies the width of the image.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageHeight">
            <summary>
            Specifies the height of the image.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageXResolution">
            <summary>
            Specifies the image resolution in width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageXResolutionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageYResolution">
            <summary>
            Specifies the image resolution in height.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageYResolutionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageResolutionUnit">
            <summary>
            Specifies the unit of the resolution information referring to a standardised unit of measurement 
            (pixels per inch, pixels per centimeter or other).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ImageResolutionUnitSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Orientation">
            <summary>
            The angle the rectangle encapsulating the page (or its Border) has to be rotated in clockwise direction
            in order to correct the present skew (negative values indicate anti-clockwise rotation).
            (The rotated image can be further referenced via “AlternativeImage”.)
            <para>Range: -179.999, 180</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Type">
            <summary>
            The type of the page within the document
            (e.g.cover page).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.PrimaryLanguage">
            <summary>
            The primary language used in the page (lower-level definitions override the page-level definition).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.PrimaryLanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.SecondaryLanguage">
            <summary>
            The secondary language used in the page (lower-level definitions override the page-level definition).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.SecondaryLanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.PrimaryScript">
            <summary>
            The primary script used in the page (lower-level definitions override the page-level definition).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.PrimaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.SecondaryScript">
            <summary>
            The secondary script used in the page (lower-level definitions override the page-level definition).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.SecondaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ReadingDirection">
            <summary>
            The direction in which text within lines should be read(order of words and characters), 
            in addition to “textLineOrder” (lower-level definitions override the page-level definition).    
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ReadingDirectionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.TextLineOrder">
            <summary>
            The order of text lines within a block, in addition to “readingDirection” 
            (lower-level definitions override the page-level definition).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.TextLineOrderSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.Conf">
            <summary>
            Confidence value for whole page (between 0 and 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPage.ConfSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageImageResolutionUnit">
            <summary>
            Specifies the unit of the resolution information referring to a standardised unit of measurement (pixels per inch, pixels per centimeter or other).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageImageResolutionUnit.PPI">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageImageResolutionUnit.PPCM">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageImageResolutionUnit.other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.FrontCover">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.BackCover">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.Title">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.TableOfContents">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.Index">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.Content">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.Blank">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPageSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPrintSpace">
            <summary>
            Determines the effective area on the paper of a printed page.
            Its size is equal for all pages of a book
            (exceptions: titlepage, multipage pictures).
            It contains all living elements (except marginals)
            like body type, footnotes, headings, running titles.
            It does not contain pagenumber (if not part of running title),
            marginals, signature mark, preview words.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlPrintSpace.Coords">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType">
            <summary>
            Text production type
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.Printed">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.Typewritten">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.HandwrittenCursive">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.HandwrittenPrintscript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.MedievalManuscript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlProductionSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingDirectionSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingDirectionSimpleType.LeftToRight">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingDirectionSimpleType.RightToLeft">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingDirectionSimpleType.TopToBottom">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingDirectionSimpleType.BottomToTop">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingOrder">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingOrder.Item">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingOrder.Conf">
            <summary>
            Confidence value (between 0 and 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlReadingOrder.ConfSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.AlternativeImage">
            <summary>
            Alternative region images (e.g.black-and-white).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Coords">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Roles">
            <summary>
            Roles the region takes (e.g. in context of a parent region).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Comments">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.Continuation">
            <summary>
            Is this region a continuation of another region
            (in previous column or page, for example)?
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegion.ContinuationSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegionRef">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegionRef.RegionRef">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegionRefIndexed">
            <summary>
            Numbered region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegionRefIndexed.Index">
            <summary>
            Position (order number) of this item within the current hierarchy level.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRegionRefIndexed.RegionRef">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation">
            <summary>
            One-to-one relation between to layout object. Use 'link'
            for loose relations and 'join' for strong relations
            (where something is fragmented for instance).
            
            <para>Examples for 'link': caption - image floating -
            paragraph paragraph - paragraph (when a paragraph is
            split across columns and the last word of the first
            paragraph DOES NOT continue in the second paragraph)
            drop-cap - paragraph (when the drop-cap is a whole word)</para>
            
            Examples for 'join': word - word (separated word at the
            end of a line) drop-cap - paragraph (when the drop-cap
            is not a whole word) paragraph - paragraph (when a
            pragraph is split across columns and the last word of
            the first paragraph DOES continue in the second
            paragraph)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.SourceRegionRef">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.TargetRegionRef">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelation.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelations">
            <summary>
            Container for one-to-one relations between layout
            objects (for example: DropCap - paragraph, caption -
            image).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelations.Relations">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelationType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelationType.Link">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRelationType.Join">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRoles">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlRoles.TableCellRole">
            <summary>
            Data for a region that takes on the role of a table cell within a parent table region.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType">
            <summary>
            iso15924 2016-07-14
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AdlmAdlam">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AfakAfaka">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AghbCaucasianAlbanian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AhomAhomTaiAhom">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ArabArabic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AranArabicNastaliqVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ArmiImperialAramaic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ArmnArmenian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.AvstAvestan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BaliBalinese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BamuBamum">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BassBassaVah">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BatkBatak">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BengBengali">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BhksBhaiksuki">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BlisBlissymbols">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BopoBopomofo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BrahBrahmi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BraiBraille">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BugiBuginese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.BuhdBuhid">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CakmChakma">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CansUnifiedCanadianAboriginalSyllabics">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CariCarian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ChamCham">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CherCherokee">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CirtCirth">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CoptCoptic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CprtCypriot">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CyrlCyrillic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.CyrsCyrillicOldChurchSlavonicVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.DevaDevanagariNagari">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.DsrtDeseretMormon">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.DuplDuployanShorthandDuployanStenography">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.EgydEgyptianDemotic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.EgyhEgyptianHieratic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.EgypEgyptianHieroglyphs">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ElbaElbasan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.EthiEthiopic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GeokKhutsuriAsomtavruliAndNuskhuri">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GeorGeorgianMkhedruli">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GlagGlagolitic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GothGothic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GranGrantha">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GrekGreek">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GujrGujarati">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.GuruGurmukhi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HanbHanwithBopomofo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HangHangul">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HaniHanHanziKanjiHanja">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HanoHanunooHanunóo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HansHanSimplifiedVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HantHanTraditionalVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HatrHatran">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HebrHebrew">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HiraHiragana">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HluwAnatolianHieroglyphs">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HmngPahawhHmong">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HrktJapaneseSyllabaries">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.HungOldHungarianHungarianRunic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.IndsIndusHarappan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ItalOldItalicEtruscanOscanEtc">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.JamoJamo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.JavaJavanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.JpanJapanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.JurcJurchen">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KaliKayahLi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KanaKatakana">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KharKharoshthi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KhmrKhmer">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KhojKhojki">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KitlKhitanlargescript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KitsKhitansmallscript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KndaKannada">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KoreKoreanaliasforHangulHan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KpelKpelle">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.KthiKaithi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LanaTaiThamLanna">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LaooLao">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LatfLatinFrakturvariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LatgLatinGaelicvariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LatnLatin">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LekeLeke">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LepcLepchaRóng">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LimbLimbu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LinaLinearA">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LinbLinearB">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LisuLisuFraser">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LomaLoma">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LyciLycian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.LydiLydian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MahjMahajani">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MandMandaicMandaean">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ManiManichaean">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MarcMarchen">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MayaMayanhieroglyphs">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MendMendeKikakui">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MercMeroiticCursive">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MeroMeroiticHieroglyphs">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MlymMalayalam">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ModiModiMoḍī">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MongMongolian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MoonMoonMooncodeMoonscriptMoontype">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MrooMroMru">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MteiMeiteiMayekMeitheiMeetei">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MultMultani">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.MymrMyanmarBurmese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NarbOldNorthArabianAncientNorthArabian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NbatNabataean">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NewaNewaNewarNewari">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NkgbNakhiGeba">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NkooNKo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.NshuNüshu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OgamOgham">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OlckOlChikiOlCemetOlSantali">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OrkhOldTurkicOrkhonRunic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OryaOriya">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OsgeOsage">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.OsmaOsmanya">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PalmPalmyrene">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PaucPauCinHau">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PermOldPermic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PhagPhagspa">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PhliInscriptionalPahlavi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PhlpPsalterPahlavi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PhlvBookPahlavi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PhnxPhoenician">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PiqdKlingonKLIpIqaD">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PlrdMiaoPollard">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.PrtiInscriptionalParthian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.RjngRejangRedjangKaganga">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.RoroRongorongo">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.RunrRunic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SamrSamaritan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SaraSarati">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SarbOldSouthArabian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SaurSaurashtra">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SgnwSignWriting">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ShawShavianShaw">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ShrdSharadaŚāradā">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SiddSiddham">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SindKhudawadiSindhi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SinhSinhala">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SoraSoraSompeng">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SundSundanese">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SyloSylotiNagri">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SyrcSyriac">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SyreSyriacEstrangeloVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SyrjSyriacWesternVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.SyrnSyriacEasternVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TagbTagbanwa">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TakrTakri">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TaleTaiLe">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TaluNewTaiLue">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TamlTamil">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TangTangut">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TavtTaiViet">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TeluTelugu">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TengTengwar">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TfngTifinaghBerber">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TglgTagalogBaybayinAlibata">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ThaaThaana">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ThaiThai">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TibtTibetan">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.TirhTirhuta">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.UgarUgaritic">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.VaiiVai">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.VispVisibleSpeech">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.WaraWarangCitiVarangKshiti">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.WoleWoleai">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.XpeoOldPersian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.XsuxCuneiformSumeroAkkadian">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.YiiiYi">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZinhCodeForInheritedScript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZmthMathematicalNotation">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZsyeSymbolsEmojiVariant">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZsymSymbols">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZxxxCodeForUnwrittenDocuments">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZyyyCodeForUndeterminedScript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.ZzzzCodeForUncodedScript">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlScriptSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlSeparatorRegion">
            <summary>
            Separators are lines that lie between columns and 
            paragraphs and can be used to logically separate
            different articles from each other.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlSeparatorRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlSeparatorRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlSeparatorRegion.Colour">
            <summary>
            The colour of the separator
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlSeparatorRegion.ColourSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole">
            <summary>
            Data for a region that takes on the role of a table cell within a parent table region.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.RowIndex">
            <summary>
            Cell position in table starting with row 0
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.ColumnIndex">
            <summary>
            Cell position in table starting with column 0
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.RowSpan">
            <summary>
            Number of rows the cell spans (optional; default is 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.RowSpanSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.ColSpan">
            <summary>
            Number of columns the cell spans (optional; default is 1)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.ColSpanSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.Header">
            <summary>
            Is the cell a column or row header?
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableCellRole.HeaderSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion">
            <summary>
            Tabular data in any form is represented with a table
            region.Rows and columns may or may not have separator
            lines; these lines are not separator regions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.Grid">
            <summary>
            Table grid (visible or virtual grid lines)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating a	region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.Rows">
            <summary>
            The number of rows present in the table
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.RowsSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.Columns">
            <summary>
            The number of columns present in the table
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.ColumnsSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.LineColour">
            <summary>
            The colour of the lines used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.LineColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.BgColour">
            <summary>
            The background colour of the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.BgColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.LineSeparators">
            <summary>
            Specifies the presence of line separators
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.LineSeparatorsSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.EmbText">
            <summary>
            Specifies whether the region also contains text
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTableRegion.EmbTextSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.Xsddouble">
            <summary>
            Examples: "123.456", "+1234.456", "-1234.456", "-.456", "-456"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdFloat">
            <summary>
            Examples: "123.456", "+1234.456", "-1.2344e56", "-.45E-6", "INF", "-INF", "NaN"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdInteger">
            <summary>
            Examples: "123456", "+00000012", "-1", "-456"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdBoolean">
            <summary>
            Examples: "true", "false", "1", "0"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdDate">
            <summary>
            Examples: "2001-10-26", "2001-10-26+02:00", "2001-10-26Z", "2001-10-26+00:00", "-2001-10-26", "-20000-04-01"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdTime">
            <summary>
            Examples: "21:32:52", "21:32:52+02:00", "19:32:52Z", "19:32:52+00:00", "21:32:52.12679"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdDateTime">
            <summary>
            Examples: "2001-10-26T21:32:52", "2001-10-26T21:32:52+02:00", "2001-10-26T19:32:52Z", "2001-10-26T19:32:52+00:00","-2001-10-26T21:32:52", "2001-10-26T21:32:52.12679"
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.XsdString">
            <summary>
            Generic text string
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextDataSimpleType.Other">
            <summary>
            An XSD type that is not listed or a custom type (use dataTypeDetails attribute).
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.PlainText">
            <summary>
            Text in a "simple" form (ASCII or extended ASCII
            as mostly used for typing). I.e.no use of
            special characters for ligatures (should be
            stored as two separate characters) etc.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.Unicode">
            <summary>
            Correct encoding of the original, always using the corresponding Unicode code point. 
            I.e. ligatures have to be represented as one character etc.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.Index">
            <summary>
            Used for sort order in case multiple TextEquivs are defined. 
            The text content with the lowest index should be interpreted as the main text content.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.Conf">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.ConfSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.DataType">
            <summary>
            Type of text content (is it free text or a number, for instance). This is only 
            a descriptive attribute, the text type is not checked during XML validation.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.DataTypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.DataTypeDetails">
            <summary>
            Refinement for dataType attribute. Can be a regular expression, for instance.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.Comments">
            <remarks/>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextEquiv.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.AlternativeImages">
            <summary>
            Alternative text line images (e.g. black-and-white)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Coords">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Baseline">
            <summary>
            Multiple connected points that mark the baseline of the glyphs
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Words">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.TextEquivs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.TextStyle">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.PrimaryLanguage">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.PrimaryLanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.PrimaryScript">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.PrimaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.SecondaryScript">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.SecondaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.ReadingDirection">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.ReadingDirectionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Production">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.ProductionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Custom">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Comments">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.Index">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.IndexSpecified">
            <remarks/>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLine.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLineOrderSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLineOrderSimpleType.TopToBottom">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLineOrderSimpleType.BottomToTop">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLineOrderSimpleType.LeftToRight">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextLineOrderSimpleType.RightToLeft">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion">
            <summary>
            Pure text is represented as a text region. This includes drop capitals, but practically 
            ornate text may be considered as a graphic.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TextLines">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TextEquivs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TextStyle">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Orientation">
            <summary>
            The angle the rectangle encapsulating the region
            has to be rotated in clockwise direction
            in order to correct the present skew
            (negative values indicate anti-clockwise rotation).
            (The rotated image can be further referenced
            via “AlternativeImage”.)
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.OrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Type">
            <summary>
            The nature of the text in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Leading">
            <summary>
            The degree of space in points between the lines of
            text(line spacing)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.LeadingSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.ReadingDirection">
            <summary>
            The direction in which text within lines
            should be read(order of words and characters),
            in addition to “textLineOrder”.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.ReadingDirectionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TextLineOrder">
            <summary>
            The order of text lines within the block,
            in addition to “readingDirection”.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.TextLineOrderSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.ReadingOrientation">
            <summary>
            The angle the baseline of text within the region
            has to be rotated(relative to the rectangle
            encapsulating the region) in clockwise direction
            in order to correct the present skew,
            in addition to “orientation”
            (negative values indicate anti-clockwise rotation).
            Range: -179.999,180
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.ReadingOrientationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Indented">
            <summary>
            Defines whether a region of text is indented or not
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.IndentedSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Align">
            <summary>
            Text align
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.AlignSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.PrimaryLanguage">
            <summary>
            The primary language used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.PrimaryLanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.SecondaryLanguage">
            <summary>
            The secondary language used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.SecondaryLanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.PrimaryScript">
            <summary>
            The primary script used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.PrimaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.SecondaryScript">
            <summary>
            The secondary script used in the region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.SecondaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.Production">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextRegion.ProductionSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Paragraph">
            <summary>
            Paragraph
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Heading">
            <summary>
            Heading
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Caption">
            <summary>
            Caption
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Header">
            <summary>
            Header
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Footer">
            <summary>
            Footer
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.PageNumber">
            <summary>
            Page number
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.DropCapital">
            <summary>
            Drop Capital, a letter a the beginning of a word that is bigger than the usual character size. Usually to start a chapter.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Credit">
            <summary>
            Credit
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Floating">
            <summary>
            Floating
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.SignatureMark">
            <summary>
            Signature mark
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.CatchWord">
            <summary>
            Catch word
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Marginalia">
            <summary>
            Marginalia
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.FootNote">
            <summary>
            Foot note
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.FootNoteContinued">
            <summary>
            Foot note - continued
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.EndNote">
            <summary>
            End note
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.TocEntry">
            <summary>
            Table of content
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.LisLabel">
            <summary>
            List
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextSimpleType.Other">
            <summary>
            Other
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle">
            <summary>
            Monospace (fixed-pitch, non-proportional) or
            proportional font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.FontFamily">
            <summary>
            For instance: Arial, Times New Roman.
            Add more information if necessary
            (e.g.blackletter, antiqua).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Serif">
            <summary>
            Serif or sans-serif typeface.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.SerifSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Monospace">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.MonospaceSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.FontSize">
            <summary>
            The size of the characters in points.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.FontSizeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.XHeight">
            <summary>
            The x-height or corpus size refers to the distance
            between the baseline and the mean line of
            lower-case letters in a typeface.
            The unit is assumed to be pixels.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Kerning">
            <summary>
            The degree of space (in points) between
            the characters in a string of text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.KerningSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.TextColour">
            <summary>
            
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.TextColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.TextColourRgb">
            <summary>
            Text colour in RGB encoded format
            <para>(red value) + (256 x green value) + (65536 x blue value).</para> 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.BgColour">
            <summary>
            Background colour
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.BgColourSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.BgColourRgb">
            <summary>
            Background colour in RGB encoded format
            <para>(red value) + (256 x green value) + (65536 x blue value).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.ReverseVideo">
            <summary>
            Specifies whether the colour of the text appears
            reversed against a background colour.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.ReverseVideoSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Bold">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.BoldSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Italic">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.ItalicSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Underlined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.UnderlinedSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.UnderlineStyle">
            <summary>
            Line style details if "underlined" is TRUE
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.UnderlineStyleSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Subscript">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.SubscriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Superscript">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.SuperscriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.Strikethrough">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.StrikethroughSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.SmallCaps">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.SmallCapsSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.LetterSpaced">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlTextStyle.LetterSpacedSpecified">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnderlineStyleSimpleType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnderlineStyleSimpleType.SingleLine">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnderlineStyleSimpleType.DoubleLine">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnderlineStyleSimpleType.Other">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnknownRegion">
            <summary>
            To be used if the region type cannot be ascertained.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup">
            <summary>
            Numbered group (contains unordered elements)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.RegionRef">
            <summary>
            Optional link to a parent region of nested regions.
            The parent region doubles as reading order group.
            Only the nested regions should be allowed as group members.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Caption">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Continuation">
            <summary>
            Is this group a continuation of another group
            (from previous column or page, for example)?
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.ContinuationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroup.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed">
            <summary>
            Indexed group containing ordered elements
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Items">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.RegionRef">
            <summary>
            Optional link to a parent region of nested regions.
            The parent region doubles as reading order group.
            Only the nested regions should be allowed as group members.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Index">
            <summary>
            Position (order number) of this item within the current hierarchy level.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Caption">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Continuation">
            <summary>
            Is this group a continuation of another group (from
            previous column or page, for example)?
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.ContinuationSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUnorderedGroupIndexed.Comments">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute">
            <summary>
            Structured custom data defined by name, type and value.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute.Name">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute.Description">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute.Type">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute.TypeSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttribute.Value">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttributeType">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttributeType.XsdString">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttributeType.XsdInteger">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttributeType.XsdBoolean">
            <remarks/>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlUserAttributeType.XsdFloat">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.AlternativeImages">
            <summary>
            Alternative word images (e.g. black-and-white)
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Coords">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Glyphs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.TextEquivs">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.TextStyle">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.UserDefined">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Labels">
            <summary>
            Semantic labels / tags
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Id">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Language">
            <summary>
            Overrides primaryLanguage attribute of parent line and/or text region
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.LanguageSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.PrimaryScript">
            <summary>
            The primary script used in the word
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.PrimaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.SecondaryScript">
            <summary>
            The secondary script used in the word 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.SecondaryScriptSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.ReadingDirection">
            <summary>
            The direction in which text within the word should be read(order of characters).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.ReadingDirectionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Production">
            <summary>
            Overrides the production attribute of the parent text line and/or text region.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.ProductionSpecified">
            <remarks/>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Custom">
            <summary>
            For generic use
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.Comments">
            <remarks/>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.PAGE.PageXmlDocument.PageXmlWord.ToString">
            <remarks/>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.SvgTextExporter">
            <summary>
            Exports a page as an SVG.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.Export.SvgTextExporter.Get(UglyToad.PdfPig.Content.Page)">
            <summary>
            Get the page contents as an SVG.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.IDlaOptions">
            <summary>
            Interface that stores options that configure the operation of methods of the document layout analysis algorithm.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.IDlaOptions.MaxDegreeOfParallelism">
            <summary>
            Gets or sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree">
            <summary>
            K-D tree data structure of <see cref="T:UglyToad.PdfPig.Core.PdfPoint"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree.#ctor(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Core.PdfPoint})">
            <summary>
            K-D tree data structure of <see cref="T:UglyToad.PdfPig.Core.PdfPoint"/>.
            </summary>
            <param name="points">The points used to build the tree.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree.FindNearestNeighbour(UglyToad.PdfPig.Core.PdfPoint,System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Int32@,System.Double@)">
            <summary>
            Get the nearest neighbour to the pivot point.
            Only returns 1 neighbour, even if equidistant points are found.
            </summary>
            <param name="pivot">The point for which to find the nearest neighbour.</param>
            <param name="distanceMeasure">The distance measure used, e.g. the Euclidian distance.</param>
            <param name="index">The nearest neighbour's index (returns -1 if not found).</param>
            <param name="distance">The distance between the pivot and the nearest neighbour (returns <see cref="F:System.Double.NaN"/> if not found).</param>
            <returns>The nearest neighbour's point.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree.FindNearestNeighbours(UglyToad.PdfPig.Core.PdfPoint,System.Int32,System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double})">
            <summary>
            Get the k nearest neighbours to the pivot point.
            Might return more than k neighbours if points are equidistant.
            <para>Use <see cref="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree.FindNearestNeighbour(UglyToad.PdfPig.Core.PdfPoint,System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Int32@,System.Double@)"/> if only looking for the (single) closest point.</para>
            </summary>
            <param name="pivot">The point for which to find the nearest neighbour.</param>
            <param name="k">The number of neighbours to return. Might return more than k neighbours if points are equidistant.</param>
            <param name="distanceMeasure">The distance measure used, e.g. the Euclidian distance.</param>
            <returns>Returns a list of tuples of the k nearest neighbours. Tuples are (element, index, distance).</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1">
            <summary>
            K-D tree data structure.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.Root">
            <summary>
            The root of the tree.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.Count">
            <summary>
            Number of elements in the tree.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.#ctor(System.Collections.Generic.IReadOnlyList{`0},System.Func{`0,UglyToad.PdfPig.Core.PdfPoint})">
            <summary>
            K-D tree data structure.
            </summary>
            <param name="elements">The elements used to build the tree.</param>
            <param name="elementsPointFunc">The function that converts the candidate elements into a <see cref="T:UglyToad.PdfPig.Core.PdfPoint"/>.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.FindNearestNeighbour(`0,System.Func{`0,UglyToad.PdfPig.Core.PdfPoint},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Int32@,System.Double@)">
            <summary>
            Get the nearest neighbour to the pivot element.
            Only returns 1 neighbour, even if equidistant points are found.
            </summary>
            <param name="pivot">The element for which to find the nearest neighbour.</param>
            <param name="pivotPointFunc">The function that converts the pivot element into a <see cref="T:UglyToad.PdfPig.Core.PdfPoint"/>.</param>
            <param name="distanceMeasure">The distance measure used, e.g. the Euclidian distance.</param>
            <param name="index">The nearest neighbour's index (returns -1 if not found).</param>
            <param name="distance">The distance between the pivot and the nearest neighbour (returns <see cref="F:System.Double.NaN"/> if not found).</param>
            <returns>The nearest neighbour's element.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.FindNearestNeighbours(`0,System.Int32,System.Func{`0,UglyToad.PdfPig.Core.PdfPoint},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double})">
            <summary>
            Get the k nearest neighbours to the pivot element.
            Might return more than k neighbours if points are equidistant.
            <para>Use <see cref="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.FindNearestNeighbour(UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree{`0}.KdTreeNode{`0},`0,System.Func{`0,UglyToad.PdfPig.Core.PdfPoint},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double})"/> if only looking for the (single) closest point.</para>
            </summary>
            <param name="pivot">The element for which to find the k nearest neighbours.</param>
            <param name="k">The number of neighbours to return. Might return more than k neighbours if points are equidistant.</param>
            <param name="pivotPointFunc">The function that converts the pivot element into a <see cref="T:UglyToad.PdfPig.Core.PdfPoint"/>.</param>
            <param name="distanceMeasure">The distance measure used, e.g. the Euclidian distance.</param>
            <returns>Returns a list of tuples of the k nearest neighbours. Tuples are (element, index, distance).</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeLeaf`1">
            <summary>
            K-D tree leaf.
            </summary>
            <typeparam name="Q"></typeparam>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeLeaf`1.IsLeaf">
            <summary>
            Return true if leaf.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeLeaf`1.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1">
            <summary>
            K-D tree node.
            </summary>
            <typeparam name="Q"></typeparam>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.L">
            <summary>
            Split value (X or Y axis).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.Value">
            <summary>
            Split point.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.LeftChild">
            <summary>
            Left child.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.RightChild">
            <summary>
            Right child.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.Element">
            <summary>
            The node's element.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.IsAxisCutX">
            <summary>
            True if this cuts with X axis, false if cuts with Y axis.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.Depth">
            <summary>
            The element's depth in the tree.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.IsLeaf">
            <summary>
            Return true if leaf.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.Index">
            <summary>
            The index of the element in the original array.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.GetLeaves">
            <summary>
            Get the leaves.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.KdTree`1.KdTreeNode`1.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.MathExtensions">
            <summary>
            Useful math extensions.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.MathExtensions.Mode(System.Collections.Generic.IEnumerable{System.Single})">
            <summary>
            Computes the mode of a sequence of <see cref="T:System.Single"/> values.
            </summary>
            <param name="array">The sequence of floats.</param>
            <returns>The mode of the sequence. Returns <see cref="F:System.Single.NaN"/> if the sequence has no mode or if it is not unique.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.MathExtensions.Mode(System.Collections.Generic.IEnumerable{System.Double})">
            <summary>
            Computes the mode of a sequence of <see cref="T:System.Double"/> values.
            </summary>
            <param name="array">The sequence of doubles.</param>
            <returns>The mode of the sequence. Returns <see cref="F:System.Double.NaN"/> if the sequence has no mode or if it is not unique.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.MathExtensions.AlmostEqualsToZero(System.Double,System.Double)">
            <summary>
            Test for almost equality to 0.
            </summary>
            <param name="number"></param>
            <param name="epsilon"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.MathExtensions.AlmostEquals(System.Double,System.Double,System.Double)">
            <summary>
            Test for almost equality.
            </summary>
            <param name="number"></param>
            <param name="other"></param>
            <param name="epsilon"></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter">
            <summary>
            Default Page Segmenter. All words are included in one block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.Instance">
            <summary>
            Create an instance of default page segmenter, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.#ctor">
            <summary>
            using default options values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.#ctor(UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions)">
            <summary>
            Create using options values.
            </summary>
            <param name="options">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions"/> to use.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.GetBlocks(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Get the text blocks using options.
            </summary>
            <param name="words">The page's words to generate text blocks for.</param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s generated by the default method.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions">
            <summary>
            Default page segmenter options.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions.MaxDegreeOfParallelism">
            <summary>
            <inheritdoc/>
            Default value is -1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions.WordSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is ' ' (space).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DefaultPageSegmenter.DefaultPageSegmenterOptions.LineSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is '\n' (new line).</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes">
            <summary>
            The Document Spectrum (Docstrum) algorithm is a bottom-up page segmentation technique based on nearest-neighbourhood
            clustering of connected components extracted from the document.
            This implementation leverages bounding boxes and does not exactly replicates the original algorithm.
            <para>See 'The document spectrum for page layout analysis.' by L. O'Gorman.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.Instance">
            <summary>
            Create an instance of Docstrum for bounding boxes page segmenter, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.#ctor">
            <summary>
            Create an instance of Docstrum for bounding boxes page segmenter using default options values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.#ctor(UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions)">
            <summary>
            Create an instance of Docstrum for bounding boxes page segmenter using options values.
            </summary>
            <param name="options">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions"/> to use.</param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetBlocks(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Get the blocks.
            </summary>
            <param name="words">The page's words to segment into <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s.</param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s generated by the document spectrum method.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetBlocks(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Word},UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Double,System.Int32,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Double,System.Int32,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Double,System.String,System.String,System.Int32)">
            <summary>
            Get the blocks. See original paper for more information.
            </summary>
            <param name="words">The words to segment into <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s.</param>
            <param name="wlBounds">Angle bounds for words to be considered as neighbours on the same line.</param>
            <param name="wlMultiplier">Multiplier that gives the maximum euclidian distance between words for building lines.
            Maximum distance will be this number times the within-line distance found by the analysis.</param>
            <param name="wlBinSize">The bin size used when building the within-line distances distribution.</param>
            <param name="blBounds">Angle bounds for words to be considered as neighbours on separate lines.</param>
            <param name="blMultiplier">Multiplier that gives the maximum perpendicular distance between
            text lines for blocking. Maximum distance will be this number times the between-line
            distance found by the analysis.</param>
            <param name="blBinSize">The bin size used when building the between-line distances distribution.</param>
            <param name="angularDifferenceBounds">The angular difference bounds between two lines to be considered in the same block. This defines if two lines are parallel enough.</param>
            <param name="epsilon">Precision when testing equalities.</param>
            <param name="wordSeparator">Separator used between words when building lines.</param>
            <param name="lineSeparator">Separator used between lines when building paragraphs.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s generated by the document spectrum method.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetSpacingEstimation(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Word},UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Int32,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Int32,System.Int32,System.Double@,System.Double@)">
            <summary>
            Estimation of within-line and between-line spacing.
            <para>This is the Docstrum algorithm's 1st step.</para>
            </summary>
            <param name="words">The list of words.</param>
            <param name="wlBounds">Angle bounds for words to be considered as neighbours on the same line.</param>
            <param name="wlBinSize">The bin size used when building the within-line distances distribution.</param>
            <param name="blBounds">Angle bounds for words to be considered as neighbours on separate lines.</param>
            <param name="blBinSize">The bin size used when building the between-line distances distribution.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
            <param name="withinLineDistance">The estimated within-line distance. Computed as the average peak value of distribution.</param>
            <param name="betweenLineDistance">The estimated between-line distance. Computed as the average peak value of distribution.</param>
            <returns>False if either 'withinLineDistance' or 'betweenLineDistance' is <see cref="F:System.Double.NaN"/>.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetPeakAverageDistance(System.Collections.Generic.IEnumerable{System.Double},System.Int32)">
            <summary>
            Get the average distance value of the peak bucket of the histogram.
            </summary>
            <param name="distances">The set of distances to average.</param>
            <param name="binLength"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetLines(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Word},System.Double,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.String,System.Int32)">
            <summary>
            Get the <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine"/>s by grouping words using nearest neighbours.
            <para>This is the Docstrum algorithm's 2nd step.</para>
            </summary>
            <param name="words">The words to segment into <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine"/>s.</param>
            <param name="maxWLDistance">The maximum within-line distance. Computed as the estimated within-line spacing times the within-line multiplier in the default implementation.</param>
            <param name="wlBounds">Angle bounds for words to be considered as neighbours on the same line.</param>
            <param name="wordSeparator">Separator used between words when building lines.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine"/>s built.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleWL(UglyToad.PdfPig.Content.Word,UglyToad.PdfPig.Content.Word)">
            <summary>
            Helper function to compute the within line angle between the pivot's bottom
            right and the candidate's bottom left points, taking in account the pivot's rotation.
            <para>-90 ≤ θ ≤ 90.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetStructuralBlocks(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine},System.Double,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Double,System.String,System.Int32)">
            <summary>
            Get the <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s.
            <para>This is the Docstrum algorithm's 3rd and final step.</para>
            <para>
            Method: We want to measure the distance between two lines using the following method:
            <br>- We check if two lines are overlapping horizontally and compute the perpendicular distance.</br>
            <br>- We check if the angle between the two line is within 'angularDifference'.</br>
            <br>- If the two lines are not overlapping or the angle is too wide, the distance is set to the infinity.</br>
            <para>If two text lines are approximately parallel, close in perpendicular distance, and they either overlap to some specified degree or are separated by only a small distance in parallel distance, then they are said to meet the criteria to belong to the same structural block.</para>
            </para>
            </summary>
            <param name="lines">The lines to segment into <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s.</param>
            <param name="maxBLDistance">The maximum between-line distance. Computed as the estimated between-line spacing times the between-line multiplier in the default implementation.</param>
            <param name="angularDifferenceBounds">The angular difference bounds between two lines to be considered in the same block. This defines if two lines are parallel enough.</param>
            <param name="epsilon">Precision when testing equalities.</param>
            <param name="lineSeparator">Separator used between lines when building paragraphs.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s built.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.PerpendicularOverlappingDistance(UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds,System.Double)">
            <summary>
            Perpendicular overlapping distance.
            </summary>
            <param name="line1"></param>
            <param name="line2"></param>
            <param name="angularDifferenceBounds"></param>
            <param name="epsilon"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.GetStructuralBlockingParameters(UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.Core.PdfLine,System.Double,System.Double@,System.Double@,System.Double@)">
            <summary>
            Get the structural blocking parameters.
            </summary>
            <param name="i"></param>
            <param name="j"></param>
            <param name="epsilon"></param>
            <param name="angularDifference">The angle between the 2 lines.<para>-180 ≤ θ ≤ 180</para></param>
            <param name="normalisedOverlap">Overlap of segment i onto j. Positive value if overlapped, negative value if nonoverlapped.<para>[-1, 1]?</para></param>
            <param name="perpendicularDistance">Signed perpendicular distance.</param>
            <returns>Return true if overlapped, false if nonoverlapped.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.PointInLine(UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint)">
            <summary>
            Helper function to check if the point belongs to the line./>
            </summary>
            <param name="pl1">Line's first point.</param>
            <param name="pl2">Line's second point.</param>
            <param name="point">The point to check.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AlmostEquals(UglyToad.PdfPig.Core.PdfLine,UglyToad.PdfPig.Core.PdfLine,System.Double)">
            <summary>
            Helper function to check if 2 lines are equal.
            </summary>
            <param name="line1"></param>
            <param name="line2"></param>
            <param name="epsilon"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBL(UglyToad.PdfPig.Content.Word,UglyToad.PdfPig.Content.Word)">
            <summary>
            Helper function to compute the between line angle between the pivot's
            and the candidate's centroid points, taking in account the pivot's rotation.
            <para>0 ≤ θ ≤ 180.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds">
            <summary>
            The bounds for the angle between two words for them to have a certain type of relationship.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds.Lower">
            <summary>
            The lower bound in degrees.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds.Upper">
            <summary>
            The upper bound in degrees.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds.#ctor(System.Double,System.Double)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.AngleBounds.Contains(System.Double)">
            <summary>
            Whether the bounds contain the angle.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions">
            <summary>
            Docstrum bounding boxes page segmenter options.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.MaxDegreeOfParallelism">
            <summary>
            <inheritdoc/>
            Default value is -1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.WordSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is ' ' (space).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.LineSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is '\n' (new line).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.Epsilon">
            <summary>
            Precision when testing equalities.
            <para>Default value is 1e-3.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.WithinLineBounds">
            <summary>
            Angle bounds for words to be considered as neighbours on the same line.
            <para>Default value is -30 ≤ θ ≤ 30.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.WithinLineMultiplier">
            <summary>
            Multiplier that gives the maximum euclidian distance between
            words for building lines. Maximum distance will be this number times the within-line
            distance found by the analysis.
            <para>Default value is 3.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.WithinLineBinSize">
            <summary>
            The bin size used when building the within-line distances distribution.
            <para>Default value is 10.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.BetweenLineBounds">
            <summary>
            Angle bounds for words to be considered as neighbours on separate lines.
            <para>Default value is 45 ≤ θ ≤ 135.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.BetweenLineMultiplier">
            <summary>
            Multiplier that gives the maximum perpendicular distance between
            text lines for blocking. Maximum distance will be this number times the between-line
            distance found by the analysis.
            <para>Default value is 1.3.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.BetweenLineBinSize">
            <summary>
            The bin size used when building the between-line distances distribution.
            <para>Default value is 10.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.DocstrumBoundingBoxes.DocstrumBoundingBoxesOptions.AngularDifferenceBounds">
            <summary>
            The angular difference bounds between two lines to be considered in the same block.
            This defines if two lines are parallel enough.
            <para>Default value is -30 ≤ θ ≤ 30.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter">
            <summary>
            Page segmentation divides a page into areas, each consisting of a layout structure (blocks, lines, etc.).
            <para> See 'Performance Comparison of Six Algorithms for Page Segmentation' by Faisal Shafait, Daniel Keysers, and Thomas M. Breuel.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenter.GetBlocks(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Get the blocks.
            </summary>
            <param name="words">The page's words to generate text blocks for.</param>
            <returns>A list of text blocks from this approach.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenterOptions">
            <summary>
            Page segmenter options interface.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenterOptions.WordSeparator">
            <summary>
            Separator used between words when building lines.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.IPageSegmenterOptions.LineSeparator">
            <summary>
            Separator used between lines when building paragraphs.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut">
            <summary>
            The recursive X-Y cut is a top-down page segmentation technique that decomposes a document
            recursively into a set of rectangular blocks. This implementation leverages bounding boxes.
            https://en.wikipedia.org/wiki/Recursive_X-Y_cut
            <para>See 'Recursive X-Y Cut using Bounding Boxes of Connected Components' by Jaekyu Ha, Robert M.Haralick and Ihsin T. Phillips</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.Instance">
            <summary>
            Create an instance of Recursive X-Y Cut page segmenter, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.#ctor">
            <summary>
            Create an instance of Recursive X-Y Cut page segmenter using default options values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.#ctor(UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions)">
            <summary>
            Create an instance of Recursive X-Y Cut page segmenter using options values.
            </summary>
            <param name="options">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions"/> to use.</param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.GetBlocks(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Get the blocks.
            </summary>
            <param name="words">The page's words to segment into <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s.</param>
            <returns>The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s generated by the Recursive X-Y cut method.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.GetBlocks(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word},System.Double,System.Func{System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Letter},System.Double},System.Func{System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Letter},System.Double},System.String,System.String)">
            <summary>
            Get the blocks.
            </summary>
            <param name="words">The words in the page.</param>
            <param name="minimumWidth">The minimum width for a block.</param>
            <param name="dominantFontWidthFunc">The function that determines the dominant font width.</param>
            <param name="dominantFontHeightFunc">The function that determines the dominant font height.</param>
            <param name="wordSeparator"></param>
            <param name="lineSeparator"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.Projection.Contains(System.Double)">
            <summary>
            Returns true if the value is greater or equal to the lower bound and smaller or equal to the upper bound.
            </summary>
            <param name="value">The value to test.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions">
            <summary>
            Recursive X-Y cut page segmenter options.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.MaxDegreeOfParallelism">
            <summary>
            <inheritdoc/>
            Default value is -1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.WordSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is ' ' (space).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.LineSeparator">
            <summary>
            <inheritdoc/>
            <para>Default value is '\n' (new line).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.MinimumWidth">
            <summary>
            The minimum width for a block.
            <para>Default value is 1.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.DominantFontWidthFunc">
            <summary>
            The function that determines the dominant font width.
            <para>Default value is the mode of the block's letters width.
            If the mode is not available, the average is used.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut.RecursiveXYCutOptions.DominantFontHeightFunc">
            <summary>
            The function that determines the dominant font height.
            <para>Default value is the mode of the block's letters height times 1.5.
            If the mode is not available, the average is used.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf">
            <summary>
            A Leaf node used in the <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut"/> algorithm, i.e. a block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.IsLeaf">
            <summary>
            Returns true if this node is a leaf, false otherwise.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.Words">
            <summary>
            The words in the leaf.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.CountWords">
            <summary>
            The number of words in the leaf.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.GetLeaves">
            <summary>
            Returns null as a leaf doesn't have leaves.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.GetLines(System.String)">
            <summary>
            Gets the lines of the leaf.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.#ctor(UglyToad.PdfPig.Content.Word[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf"/>.
            </summary>
            <param name="words">The words contained in the leaf.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf.#ctor(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYLeaf"/>.
            </summary>
            <param name="words">The words contained in the leaf.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode">
            <summary>
            A Node used in the <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.RecursiveXYCut"/> algorithm.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.IsLeaf">
            <summary>
            Returns true if this node is a leaf, false otherwise.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.BoundingBox">
            <summary>
            The rectangle completely containing the node.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.Children">
            <summary>
            The children of the node.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.#ctor(UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode"/>.
            </summary>
            <param name="children">The node's children.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.#ctor(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode"/>.
            </summary>
            <param name="children">The node's children.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.CountWords">
            <summary>
            Recursively counts the words included in this node.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.GetLeaves">
            <summary>
            Recursively gets the leaves (last nodes) of this node.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.PageSegmenter.XYNode.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.DefaultReadingOrderDetector">
            <summary>
            This detector does nothing, no ordering takes place.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.DefaultReadingOrderDetector.Instance">
            <summary>
            Create an instance of default reading order detector, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.DefaultReadingOrderDetector"/>.
            <para>This detector does nothing, no ordering takes place.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.DefaultReadingOrderDetector.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock})">
            <summary>
            Gets the blocks in reading order and sets the <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ReadingOrder"/>.
            </summary>
            <param name="textBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s to order.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.IReadingOrderDetector">
            <summary>
            Reading order detector determines the page's blocks reading order.
            <para>Note: Make sure you use <see cref="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.SetReadingOrder(System.Int32)"/> to set each <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/> reading order when implementing <see cref="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.IReadingOrderDetector.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock})"/>.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.IReadingOrderDetector.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock})">
            <summary>
            Gets the blocks in reading order and sets the <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ReadingOrder"/>.
            </summary>
            <param name="textBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s to order.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.ReadingOrderHelper">
            <summary>
            Helper functions for words and lines ordering.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.ReadingOrderHelper.OrderByReadingOrder(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word})">
            <summary>
            Order words by reading order in a line.
            <para>Assumes LtR and accounts for rotation.</para>
            </summary>
            <param name="words"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.ReadingOrderHelper.OrderByReadingOrder(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine})">
            <summary>
            Order lines by reading order in a block.
            <para>Assumes TtB and accounts for rotation.</para>
            </summary>
            <param name="lines"></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.RenderingReadingOrderDetector">
            <summary>
            Algorithm that retrieve the blocks' reading order using rendering order (TextSequence).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.RenderingReadingOrderDetector.Instance">
            <summary>
            Create an instance of rendering reading order detector, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.RenderingReadingOrderDetector"/>.
            <para>This detector uses the rendering order (TextSequence).</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.RenderingReadingOrderDetector.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock})">
            <summary>
            Gets the blocks in reading order and sets the <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ReadingOrder"/>.
            </summary>
            <param name="textBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s to order.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector">
            <summary>
            Algorithm that retrieve the blocks' reading order using spatial reasoning (Allen’s interval relations) and possibly the rendering order (TextSequence).
            <para>See section 4.1 of 'Unsupervised document structure analysis of digital scientific articles' by S. Klampfl, M. Granitzer, K. Jack, R. Kern
            and 'Document Understanding for a Broad Class of Documents' by L. Todoran, M. Worring, M. Aiello and C. Monz.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRules">
            <summary>
            The rules encoding the spatial reasoning constraints.
            <para>See 'Document Understanding for a Broad Class of Documents' by L. Todoran, M. Worring, M. Aiello and C. Monz.</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRules.Basic">
            <summary>
            Basic spacial reasoning.
            <para>In western culture the reading order is from left to right and from top to bottom.</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRules.RowWise">
            <summary>
            Text-blocks are read in rows from left-to-right, top-to-bottom.
            <para>The diagonal direction 'left-bottom to top-right' cannot be present among the Basic relations allowed.</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRules.ColumnWise">
            <summary>
            Text-blocks are read in columns, from top-to-bottom and from left-to-right.
            <para>The diagonal direction 'right-top to bottom-left' cannot be present among the Basic relations allowed.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.Instance">
            <summary>
            Create an instance of unsupervised reading order detector, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector"/>.
            <para>This detector uses spatial reasoning (Allen’s interval relations) and possibly the rendering order (TextSequence).</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.UseRenderingOrder">
            <summary>
            Whether or not to also use the rendering order, as indicated by the TextSequence.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRule">
            <summary>
            The rule to be used that encodes the spatial reasoning constraints.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.T">
            <summary>
            The tolerance parameter T. If two coordinates are closer than T they are considered equal.
            <para>This flexibility is necessary because due to the inherent noise in the PDF extraction text blocks in the
            same column might not be exactly aligned.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.#ctor(System.Double,UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.SpatialReasoningRules,System.Boolean)">
            <summary>
            Algorithm that retrieve the blocks' reading order using spatial reasoning (Allen’s interval relations) and possibly the rendering order (TextSequence).
            </summary>
            <param name="T">The tolerance parameter T. If two coordinates are closer than T they are considered equal.
            This flexibility is necessary because due to the inherent noise in the PDF extraction text blocks in the
            same column might not be exactly aligned.</param>
            <param name="spatialReasoningRule">The rule to be used that encodes the spatial reasoning constraints.</param>
            <param name="useRenderingOrder">Whether or not to also use the rendering order, as indicated by the TextSequence.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.Get(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock})">
            <summary>
            Gets the blocks in reading order and sets the <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ReadingOrder"/>.
            </summary>
            <param name="textBlocks">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>s to order.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.GetBeforeInReading(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double)">
            <summary>
            Rule encoding the fact that in western culture the reading order is from left to right and from top to bottom.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="T">The tolerance parameter T.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.GetBeforeInReadingVertical(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double)">
            <summary>
            Column-wise: text-blocks are read in columns, from top-to-bottom and from left-to-right.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="T">The tolerance parameter T.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.GetBeforeInReadingHorizontal(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double)">
            <summary>
            Row-wise: text-blocks are read in rows from left-to-right, top- to-bottom.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="T">The tolerance parameter T.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.GetIntervalRelationX(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double)">
            <summary>
            Gets the Thick Boundary Rectangle Relations (TBRR) for the X coordinate.
            <para>The Thick Boundary Rectangle Relations (TBRR) is a set of qualitative relations representing the spatial relations of the document objects on the page.
            For every pair of document objects a and b, one X and one Y interval relation hold. If one considers the pair in reversed
            order, the inverse interval relation holds. Therefore the directed graph g_i representing these relations is complete.</para>
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="T">The tolerance parameter T. If two coordinates are closer than T they are considered equal.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.GetIntervalRelationY(UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock,System.Double)">
            <summary>
            Gets the Thick Boundary Rectangle Relations (TBRR) for the Y coordinate.
            <para>The Thick Boundary Rectangle Relations (TBRR) is a set of qualitative relations representing the spatial relations of the document objects on the page.
            For every pair of document objects a and b, one X and one Y interval relation hold. If one considers the pair in reversed
            order, the inverse interval relation holds. Therefore the directed graph g_i representing these relations is complete.</para>
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="T">The tolerance parameter T. If two coordinates are closer than T they are considered equal.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations">
            <summary>
            Allen’s interval thirteen relations.
            <para>See https://en.wikipedia.org/wiki/Allen%27s_interval_algebra</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Unknown">
            <summary>
            Unknown interval relations.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Precedes">
            <summary>
            X takes place before Y.
            <para>|____X____|......................</para>
            <para>......................|____Y____|</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Meets">
            <summary>
            X meets Y.
            <para>|____X____|.................</para>
            <para>.................|____Y____|</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Overlaps">
            <summary>
            X overlaps with Y.
            <para>|______X______|.................</para>
            <para>.................|______Y______|</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Starts">
            <summary>
            X starts Y.
            <para>|____X____|.................</para>
            <para>|_____Y_____|..............</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.During">
            <summary>
            X during Y.
            <para>........|____X____|.........</para>
            <para>.....|______Y______|.....</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Finishes">
            <summary>
            X finishes Y.
            <para>.................|____X____|</para>
            <para>..............|_____Y_____|</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.PrecedesI">
            <summary>
            Inverse precedes.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.MeetsI">
            <summary>
            Inverse meets.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.OverlapsI">
            <summary>
            Inverse overlaps.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.StartsI">
            <summary>
            Inverse Starts.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.DuringI">
            <summary>
            Inverse during.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.FinishesI">
            <summary>
            Inverse finishes.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.ReadingOrderDetector.UnsupervisedReadingOrderDetector.IntervalRelations.Equals">
            <summary>
            X is equal to Y.
            <para>..........|____X____|............</para>
            <para>..........|____Y____|............</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock">
            <summary>
            A block of text.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.Separator">
            <summary>
            The separator used between lines in the block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.Text">
            <summary>
            The text of the block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.TextOrientation">
            <summary>
            The text orientation of the block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.BoundingBox">
            <summary>
            The rectangle completely containing the block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.TextLines">
            <summary>
            The text lines contained in the block.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ReadingOrder">
            <summary>
            The reading order index. Starts at 0. A value of -1 means the block is not ordered.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.#ctor(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine},System.String)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>.
            </summary>
            <param name="lines">The words contained in the line, in the correct order.</param>
            <param name="separator">The separator used between lines in the block.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.SetReadingOrder(System.Int32)">
            <summary>
            Sets the <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock"/>'s reading order.
            </summary>
            <param name="readingOrder"></param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextBlock.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextEdgesExtractor">
            <summary>
            Text edges extractor. Text edges are where words have either their BoundingBox's left, right or mid coordinates aligned on the same vertical line.
            <para>Useful to detect text columns, tables, justified text, lists, etc.</para>
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.TextEdgesExtractor.edgesFuncs">
            <summary>
            Functions used to define left, middle and right edges.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextEdgesExtractor.GetEdges(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word},System.Int32,System.Int32)">
            <summary>
            Get the text edges.
            </summary>
            <param name="pageWords">The words in the page.</param>
            <param name="minimumElements">The minimum number of elements to define a text edge.</param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.EdgeType">
            <summary>
            The type of text edge.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.EdgeType.Left">
            <summary>
            Text edges where words have their BoundingBox's left coordinate aligned on the same vertical line.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.EdgeType.Mid">
            <summary>
            Text edges where words have their BoundingBox's mid coordinate aligned on the same vertical line.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.EdgeType.Right">
            <summary>
            Text edges where words have their BoundingBox's right coordinate aligned on the same vertical line.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor">
            <summary>
            Extracts text from a document based on the content order in the file.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.GetText(UglyToad.PdfPig.Content.Page,System.Boolean)">
            <summary>
            Gets a human readable representation of the text from the page based on
            the letter order of the original PDF document.
            </summary>
            <param name="page">A page from the document.</param>
            <param name="addDoubleNewline">Whether to include a double new-line when the text is likely to be a new paragraph.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.GetText(UglyToad.PdfPig.Content.Page,UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.Options)">
            <summary>
            Gets a human readable representation of the text from the page based on
            the letter order of the original PDF document.
            </summary>
            <param name="page">A page from the document.</param>
            <param name="options">Control various aspects of the generated text.</param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.Options">
            <summary>
            Options controlling the text generation algorithm.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.Options.SeparateParagraphsWithDoubleNewline">
            <summary>
            Whether to include a double new-line when the text is likely to be a new paragraph.
            Default <see langword="false"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor.ContentOrderTextExtractor.Options.ReplaceWhitespaceWithSpace">
            <summary>
            Whether to replace all whitespace characters (except line breaks) with single space ' '
            character. Default <see langword="false"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine">
            <summary>
            A line of text.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.Separator">
            <summary>
            The separator used between words in the line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.Text">
            <summary>
            The text of the line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.TextOrientation">
            <summary>
            The text orientation of the line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.BoundingBox">
            <summary>
            The rectangle completely containing the line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.Words">
            <summary>
            The words contained in the line.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.#ctor(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Word},System.String)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine"/>.
            </summary>
            <param name="words">The words contained in the line, in the correct order.</param>
            <param name="separator">The separator used between words in the line.</param>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.TextLine.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WhitespaceCoverExtractor">
            <summary>
            A top-down algorithm that finds a cover of the background whitespace of a document in terms of maximal empty rectangles.
            <para>See Section 3.2 of 'High precision text extraction from PDF documents' by Øyvind Raddum Berg and Section 2 of 'Two geometric algorithms for layout analysis' by Thomas M. Breuel.</para>
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WhitespaceCoverExtractor.GetWhitespaces(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word},System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.IPdfImage},System.Int32,System.Int32)">
            <summary>
            Gets the cover of the background whitespace of a page in terms of maximal empty rectangles.
            </summary>
            <param name="words">The words in the page.</param>
            <param name="images">The images in the page.</param>
            <param name="maxRectangleCount">The maximum number of rectangles to find.</param>
            <param name="maxBoundQueueSize">The maximum size of the queue used in the algorithm.</param>
            <returns>The identified whitespace rectangles.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WhitespaceCoverExtractor.GetWhitespaces(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.Word},System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Content.IPdfImage},System.Double,System.Double,System.Int32,System.Double,System.Int32)">
            <summary>
            Gets the cover of the background whitespace of a page in terms of maximal empty rectangles.
            </summary>
            <param name="words">The words in the page.</param>
            <param name="images">The images in the page.</param>
            <param name="minWidth">Lower bounds for the width of rectangles.</param>
            <param name="minHeight">Lower bounds for the height of rectangles.</param>
            <param name="maxRectangleCount">The maximum number of rectangles to find.</param>
            <param name="whitespaceFuzziness">Constant value to allow candidate whitespace rectangle to overlap the
            surrounding obstacles by some percent. Default value is 15%.</param>
            <param name="maxBoundQueueSize">The maximum size of the queue used in the algorithm.</param>
            <returns>The identified whitespace rectangles.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WhitespaceCoverExtractor.GetWhitespaces(System.Collections.Generic.IEnumerable{UglyToad.PdfPig.Core.PdfRectangle},System.Double,System.Double,System.Int32,System.Double,System.Int32)">
            <summary>
            Gets the cover of the background whitespace of a page in terms of maximal empty rectangles.
            </summary>
            <param name="boundingboxes">The list of obstacles' bounding boxes in the page.</param>
            <param name="minWidth">Lower bounds for the width of rectangles.</param>
            <param name="minHeight">Lower bounds for the height of rectangles.</param>
            <param name="maxRectangleCount">The maximum number of rectangles to find.</param>
            <param name="whitespaceFuzziness">Constant value to allow candidate whitespace rectangle to overlap the
            surrounding obstacles by some percent. Default value is 15%.</param>
            <param name="maxBoundQueueSize">The maximum size of the queue used in the algorithm.</param>
            <returns>The identified whitespace rectangles.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WhitespaceCoverExtractor.QueueEntry.ScoringFunction(UglyToad.PdfPig.Core.PdfRectangle)">
            <summary>
            The scoring function Q(r) which is subsequently used to sort a priority queue.
            </summary>
            <param name="rectangle"></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.IWordExtractorOptions">
            <summary>
            Word extractor options interface.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor">
            <summary>
            Nearest Neighbour Word Extractor.
            This implementation leverages bounding boxes.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.Instance">
            <summary>
            Create an instance of Nearest Neighbour Word Extractor, <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.#ctor">
            <summary>
            Create an instance of Nearest Neighbour Word Extractor using default options values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.#ctor(UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions)">
            <summary>
            Create an instance of Nearest Neighbour Word Extractor using options values.
            </summary>
            <param name="options">The <see cref="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions"/> to use.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.GetWords(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Letter})">
            <summary>
            Get the words.
            </summary>
            <param name="letters">The page's letters to group into <see cref="T:UglyToad.PdfPig.Content.Word"/>s.</param>
            <returns>The <see cref="T:UglyToad.PdfPig.Content.Word"/>s generated by the nearest neighbour method.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.GetWords(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Content.Letter},System.Func{UglyToad.PdfPig.Content.Letter,UglyToad.PdfPig.Content.Letter,System.Double},System.Func{UglyToad.PdfPig.Core.PdfPoint,UglyToad.PdfPig.Core.PdfPoint,System.Double},System.Func{UglyToad.PdfPig.Content.Letter,System.Boolean},System.Func{UglyToad.PdfPig.Content.Letter,UglyToad.PdfPig.Content.Letter,System.Boolean},System.Int32)">
            <summary>
            Gets the words.
            </summary>
            <param name="letters">The letters in the page.</param>
            <param name="maxDistanceFunction">The function that determines the maximum distance between two letters (start and end base line points),
            e.g. Max(GlyphRectangle.Width) x 20%.
            <para>If the distance between the two letters is greater, a new word will be created.</para></param>
            <param name="distMeasure">The distance measure between two letters (start and end base line points),
            e.g. the Manhattan distance.</param>
            <param name="filterPivotFunction"></param>
            <param name="filterFunction">Function used to filter out connection between letters, e.g. check if the letters have the same color.
            <para>If the function returns false, a new word will be created.</para></param>
            <param name="maxDegreeOfParallelism">Sets the maximum number of concurrent tasks enabled.
            <para>A positive property value limits the number of concurrent operations to the set value.
            If it is -1, there is no limit on the number of concurrently running operations.</para></param>
        </member>
        <member name="T:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions">
            <summary>
            Nearest neighbour word extractor options.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.MaxDegreeOfParallelism">
            <summary>
            <inheritdoc/>
            Default value is -1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.MaximumDistance">
            <summary>
            The maximum distance between two letters (start and end base line points) within the same word, as a function of the two letters.
            <para>If the distance between the two letters is greater than this maximum, they will belong to different words.</para>
            <para>Default value is 20% of the Max(Width, PointSize) of both letters. If <see cref="T:UglyToad.PdfPig.Content.TextOrientation"/> is Other, this distance is doubled.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasure">
            <summary>
            The default distance measure used between two letters (start and end base line points).
            <para>Default value is the Euclidean distance.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasureAA">
            <summary>
            The distance measure used between two letters (start and end base line points) with axis aligned <see cref="T:UglyToad.PdfPig.Content.TextOrientation"/>.
            <para>Only used if <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.GroupByOrientation"/> is set to <c>true</c>.</para>
            <para>Default value is the Manhattan distance.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.Filter">
            <summary>
            Function used to filter out connection between letters, e.g. check if the letters have the same color.
            If the function returns <c>false</c>, letters will belong to different words.
            <para>Default value checks whether the neighbour is a white space or not. If it is the case, it returns <c>false</c>.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.FilterPivot">
            <summary>
            Function used prior searching for the nearest neighbour. If return false, no search will be done.
            <para>Default value checks whether the current letter is a white space or not. If it is the case, it returns false and no search is done.</para>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.GroupByOrientation">
            <summary>
            If <c>true</c>, letters will be grouped by <see cref="T:UglyToad.PdfPig.Content.TextOrientation"/> before processing.
            The <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasureAA"/> will be used on axis aligned letters, and the <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasure"/> on others.
            <para>If <c>false</c>, <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasure"/> will be used for all letters, and <see cref="P:UglyToad.PdfPig.DocumentLayoutAnalysis.WordExtractor.NearestNeighbourWordExtractor.NearestNeighbourWordExtractorOptions.DistanceMeasureAA"/> won't be used.</para>
            <para>Default value is true.</para>
            </summary>
        </member>
    </members>
</doc>
