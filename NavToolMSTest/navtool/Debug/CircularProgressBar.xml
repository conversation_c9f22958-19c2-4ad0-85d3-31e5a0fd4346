<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CircularProgressBar</name>
    </assembly>
    <members>
        <member name="T:CircularProgressBar.CircularProgressBar">
            <summary>
                The circular progress bar windows form control
            </summary>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:CircularProgressBar.CircularProgressBar" /> class.
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.AnimationFunction">
            <summary>
                Sets a known animation function.
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.AnimationSpeed">
            <summary>
                Gets or sets the animation speed in milliseconds.
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.CustomAnimationFunction">
            <summary>
                Sets a custom animation function.
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.Font">
            <summary>
                Gets or sets the font of text in the <see cref="T:CircularProgressBar.CircularProgressBar" />.
            </summary>
            <returns>
                The <see cref="T:System.Drawing.Font" /> of the text. The default is the font set by the container.
            </returns>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.InnerColor">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.InnerMargin">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.InnerWidth">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.OuterColor">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.OuterMargin">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.OuterWidth">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.ProgressColor">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.ProgressWidth">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SecondaryFont">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.StartAngle">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SubscriptColor">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SubscriptMargin">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SubscriptText">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SuperscriptColor">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SuperscriptMargin">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.SuperscriptText">
            <summary>
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.Text">
            <summary>
                Gets or sets the text in the <see cref="T:CircularProgressBar.CircularProgressBar" />.
            </summary>
        </member>
        <member name="P:CircularProgressBar.CircularProgressBar.TextMargin">
            <summary>
            </summary>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnLocationChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
                Raises the <see cref="E:System.Windows.Forms.Control.Paint" /> event.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that contains the event data. </param>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnParentBackColorChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnParentBackgroundImageChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnParentChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.OnStyleChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.InitializeContinues(System.Boolean)">
            <summary>
                Initialize the animation for the continues styling
            </summary>
            <param name="firstTime">True if it is the first execution of this function, otherwise false</param>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.InitializeMarquee(System.Boolean)">
            <summary>
                Initialize the animation for the marquee styling
            </summary>
            <param name="firstTime">True if it is the first execution of this function, otherwise false</param>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.ParentOnInvalidated(System.Object,System.Windows.Forms.InvalidateEventArgs)">
            <summary>
                Occurs when parent's display requires redrawing.
            </summary>
            <param name="sender"></param>
            <param name="invalidateEventArgs"></param>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.ParentOnResize(System.Object,System.EventArgs)">
            <summary>
                Occurs when the parent resized.
            </summary>
            <param name="sender"></param>
            <param name="eventArgs"></param>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.RecreateBackgroundBrush">
            <summary>
                Update or create the brush used for drawing the background
            </summary>
        </member>
        <member name="M:CircularProgressBar.CircularProgressBar.StartPaint(System.Drawing.Graphics)">
            <summary>
                The function responsible for painting the control
            </summary>
            <param name="g">The <see cref="T:System.Drawing.Graphics" /> object to draw into</param>
        </member>
    </members>
</doc>
