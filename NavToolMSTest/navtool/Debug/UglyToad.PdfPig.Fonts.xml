<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UglyToad.PdfPig.Fonts</name>
    </assembly>
    <members>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics">
            <summary>
            The global metrics for a font program and the metrics of each character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.AfmVersion">
            <summary>
            Version of the Adobe Font Metrics specification used to generate this file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Comments">
            <summary>
            Any comments in the file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.MetricSets">
            <summary>
            The writing directions described by these metrics.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.FontName">
            <summary>
            Font name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.FullName">
            <summary>
            Font full name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.FamilyName">
            <summary>
            Font family name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Weight">
            <summary>
            Font weight.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.BoundingBox">
            <summary>
            Minimum bounding box for all characters in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Version">
            <summary>
            Font program version identifier.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Notice">
            <summary>
            Font name trademark or copyright notice.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.EncodingScheme">
            <summary>
            String indicating the default encoding vector for this font program.
            Common ones are AdobeStandardEncoding and JIS12-88-CFEncoding.
            Special font programs might state FontSpecific.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.MappingScheme">
            <summary>
            Describes the mapping scheme.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.EscapeCharacter">
            <summary>
            The bytes value of the escape-character used if this font is escape-mapped.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.CharacterSet">
            <summary>
            Describes the character set of this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Characters">
            <summary>
            The number of characters in this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.IsBaseFont">
            <summary>
            Whether this is a base font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.VVector">
            <summary>
            A vector from the origin of writing direction 0 to direction 1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.IsFixedV">
            <summary>
            Whether <see cref="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.VVector"/> is the same for every character in this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.CapHeight">
            <summary>
            Usually the y-value of the top of capital 'H'.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.XHeight">
            <summary>
            Usually the y-value of the top of lowercase 'x'.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Ascender">
            <summary>
            Usually the y-value of the top of lowercase 'd'.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.Descender">
            <summary>
            Usually the y-value of the bottom of lowercase 'p'.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.UnderlinePosition">
            <summary>
            Distance from the baseline for underlining.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.UnderlineThickness">
            <summary>
            Width of the line for underlining.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.ItalicAngle">
            <summary>
            Angle in degrees counter-clockwise from the vertical of the vertical linea.
            Zero for non-italic fonts.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.CharacterWidth">
            <summary>
            If present all characters have this width and height.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.HorizontalStemWidth">
            <summary>
            Horizontal stem width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.VerticalStemWidth">
            <summary>
            Vertical stem width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.CharacterMetrics">
            <summary>
            Metrics for the individual characters.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.#ctor(System.Decimal,System.Collections.Generic.IReadOnlyList{System.String},System.Int32,System.String,System.String,System.String,System.String,UglyToad.PdfPig.Core.PdfRectangle,System.String,System.String,System.String,System.Int32,System.Int32,System.String,System.Int32,System.Boolean,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector,System.Boolean,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize,System.Decimal,System.Decimal,System.Collections.Generic.IReadOnlyDictionary{System.String,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics.ToString">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.FontName">
            <summary>
            Name of the font as seen by PostScript.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.FullName">
            <summary>
            The full text name of the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.FamilyName">
            <summary>
            The name of the typeface family for the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.Weight">
            <summary>
            The weight of the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.ItalicAngle">
            <summary>
            Angle in degrees counter-clockwise from vertical of vertical strokes of the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.IsFixedPitch">
            <summary>
            Whether the font is monospaced or not.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.PdfBoundingBox">
            <summary>
            The dimensions of the font bounding box.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.UnderlinePosition">
            <summary>
            Distance from the baseline for underlining.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.UnderlineThickness">
            <summary>
            The stroke width for underlining.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.Version">
            <summary>
            Version identifier for the font program.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.Notice">
            <summary>
            Font name trademark or copyright notice.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.MappingScheme">
            <summary>
            Code describing mapping scheme for a non base font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.CharacterSet">
            <summary>
            The character set of this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.CapHeight">
            <summary>
            The y-value of the top of a capital H.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.XHeight">
            <summary>
            The y-value of the top of lowercase x.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.Ascender">
            <summary>
            Generally the y-value of the top of lowercase d.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.Descender">
            <summary>
            The y-value of the bottom of lowercase p.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.StdHw">
            <summary>
            Width of horizontal stems.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsBuilder.StdVw">
            <summary>
            Width of vertical stems.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize">
            <summary>
            The x and y components of the width vector of the font's characters.
            Presence implies that IsFixedPitch is true.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize.X">
            <summary>
            The horizontal width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize.Y">
            <summary>
            The vertical width.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize.#ctor(System.Double,System.Double)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsCharacterSize.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsEncoding">
            <inheritdoc />
            <summary>
            An <see cref="T:UglyToad.PdfPig.Fonts.Encodings.Encoding" /> from an Adobe Font Metrics file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsEncoding.#ctor(UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsEncoding"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric">
            <summary>
            The metrics for an individual character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.CharacterCode">
            <summary>
            Character code.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.Name">
            <summary>
            PostScript language character name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.Width">
            <summary>
            Width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.WidthDirection0">
            <summary>
            Width for writing direction 0.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.WidthDirection1">
            <summary>
            Width for writing direction 1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.VVector">
            <summary>
            Vector from origin of writing direction 1 to origin of writing direction 0.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.BoundingBox">
            <summary>
            Character bounding box.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.Ligature">
            <summary>
            Ligature information.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.#ctor(System.Int32,System.String,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector,UglyToad.PdfPig.Core.PdfRectangle,UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsIndividualCharacterMetric.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature">
            <summary>
            A ligature in an Adobe Font Metrics individual character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature.Successor">
            <summary>
            The character to join with to form a ligature.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature.Value">
            <summary>
            The current character.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature.#ctor(System.String,System.String)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsLigature.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser">
            <summary>
            Parses files in the Adobe Font Metrics (AFM) format.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Comment">
            <summary>
            This is a comment in a AFM file.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartFontMetrics">
            <summary>
            This is the constant used in the AFM file to start a font metrics item.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndFontMetrics">
            <summary>
            This is the constant used in the AFM file to end a font metrics item.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.FontName">
            <summary>
            The font name.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.FullName">
            <summary>
            The full name.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.FamilyName">
            <summary>
            The family name.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Weight">
            <summary>
            The weight.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.FontBbox">
            <summary>
            The bounding box.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Version">
            <summary>
            The version of the font.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Notice">
            <summary>
            The notice.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EncodingScheme">
            <summary>
            The encoding scheme.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.MappingScheme">
            <summary>
            The mapping scheme.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EscChar">
            <summary>
            The escape character.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharacterSet">
            <summary>
            The character set.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Characters">
            <summary>
            The characters attribute.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.IsBaseFont">
            <summary>
            Whether this is a base font.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.VVector">
            <summary>
            The V Vector attribute.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.IsFixedV">
            <summary>
            Whether V is fixed.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CapHeight">
            <summary>
            The cap height.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.XHeight">
            <summary>
            The X height.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Ascender">
            <summary>
            The ascender attribute.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Descender">
            <summary>
            The descender attribute.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.UnderlinePosition">
            <summary>
            The underline position.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.UnderlineThickness">
            <summary>
            The underline thickness.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.ItalicAngle">
            <summary>
            The italic angle.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharWidth">
            <summary>
            The character width.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.IsFixedPitch">
            <summary>
            Determines if fixed pitch.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartCharMetrics">
            <summary>
            The start of the character metrics.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndCharMetrics">
            <summary>
            The end of the character metrics.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsC">
            <summary>
            The character metrics c value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsCh">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsWx">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW0X">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW1X">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsWy">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW0Y">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW1Y">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW0">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsW1">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsVv">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsN">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsB">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.CharmetricsL">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StdHw">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StdVw">
            <summary>
            The character metrics value.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartTrackKern">
            <summary>
            This is the start of the track kern data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndTrackKern">
            <summary>
            This is the end of the track kern data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartKernData">
            <summary>
            This is the start of the kern data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndKernData">
            <summary>
            This is the end of the kern data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartKernPairs">
            <summary>
            This is the start of the kern pairs data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndKernPairs">
            <summary>
            This is the end of the kern pairs data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartKernPairs0">
            <summary>
            This is the start of the kern pairs data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartKernPairs1">
            <summary>
            This is the start of the kern pairs data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.StartComposites">
            <summary>
            This is the start of the composite data section.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.EndComposites">
            <summary>
            This is the end of the composite data section.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Cc">
            <summary>
            This is a composite character.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Pcc">
            <summary>
            This is a composite character part.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.KernPairKp">
            <summary>
            This is a kern pair.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.KernPairKph">
            <summary>
            This is a kern pair.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.KernPairKpx">
            <summary>
            This is a kern pair.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.KernPairKpy">
            <summary>
            This is a kern pair.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsParser.Parse(UglyToad.PdfPig.Core.IInputBytes,System.Boolean)">
            <summary>
            Parse the font metrics from the input bytes.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector">
            <summary>
            A vector in the Adobe Font Metrics.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector.X">
            <summary>
            The x component of the vector.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector.Y">
            <summary>
            The y component of the vector.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector.#ctor(System.Double,System.Double)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsVector.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsWritingDirections">
            <summary>
            The meaning of the metric sets field.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsWritingDirections.Direction0Only">
            <summary>
            Writing direction 0 only.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsWritingDirections.Direction1Only">
            <summary>
            Writing direction 1 only.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetricsWritingDirections.Direction0And1">
            <summary>
            Writing direction 0 and 1.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary">
            <summary>
            Holds common properties between Adobe Type 1 and Compact Font Format private dictionaries.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.DefaultBlueScale">
            <summary>
            Default value of <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueScale"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.DefaultExpansionFactor">
            <summary>
            Default value of <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.ExpansionFactor"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.DefaultBlueFuzz">
            <summary>
            Default value of <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueFuzz"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.DefaultBlueShift">
            <summary>
            Default value of <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueShift"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.DefaultLanguageGroup">
            <summary>
            Default value of <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.LanguageGroup"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueValues">
            <summary>
            Required. An array containing an even number of integers.
            The first pair is the baseline overshoot position and the baseline.
            All following pairs describe top-zones.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.OtherBlues">
            <summary>
            Optional: Pairs of integers similar to <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueValues"/>.
            These only describe bottom zones.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.FamilyBlues">
            <summary>
            Optional: Integer pairs similar to <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueValues"/> however these
            are used to enforce consistency across a font family when there are small differences (&lt;1px) in
            font alignment.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.FamilyOtherBlues">
            <summary>
            Optional: Integer pairs similar to <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.OtherBlues"/> however these
            are used to enforce consistency across a font family with small differences 
            in alignment similarly to <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.FamilyBlues"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueScale">
            <summary>
            Optional: The point size at which overshoot suppression stops.
            The value is a related to the number of pixels tall that one character space unit will be
            before overshoot suppression is switched off. Overshoot suppression enforces features to snap
            to alignment zones when the point size is below that affected by this value.
            Default: 0.039625
            </summary>
            <example>
            A blue scale of 0.039625 switches overshoot suppression off at 10 points
            on a 300 dpi device using the formula (for 300 dpi):
            BlueScale = (pointsize - 0.49)/240
            For example, if you wish overshoot suppression to turn off at 11
            points on a 300-dpi device, you should set BlueScale to
            (11 − 0.49) ÷ 240 or 0.04379
            </example>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueShift">
            <summary>
            Optional: The character space distance beyond the flat position of alignment zones
            at which overshoot enforcement occurs.
            Default: 7
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueFuzz">
            <summary>
            Optional: The number of character space units to extend an alignment zone
            on a horizontal stem.
            If the top or bottom of a horizontal stem is within BlueFuzz units outside a top-zone
            then the stem top/bottom is treated as if it were within the zone.
            Default: 1
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StandardHorizontalWidth">
            <summary>
            Optional: The dominant width of horizontal stems vertically in character space units.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StandardVerticalWidth">
            <summary>
            Optional: The dominant width of vertical stems horizontally in character space units.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StemSnapHorizontalWidths">
            <summary>
            Optional: Up to 12 numbers with the most common widths for horizontal stems vertically in character space units.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StemSnapVerticalWidths">
            <summary>
            Optional: Up to 12 numbers with the most common widths for vertical stems horizontally in character space units.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.ForceBold">
            <summary>
            Optional: At small sizes at low resolutions this controls whether bold characters should appear thicker using
            special techniques.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.LanguageGroup">
            <summary>
            Optional: Language group 0 includes Latin, Greek and Cyrillic as well as similar alphabets.
            Language group 1 includes Chinese, Japanese Kanji and Korean Hangul as well as similar alphabets.
            Default: 0
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.ExpansionFactor">
            <summary>
            Optional: The limit for changing the size of a character bounding box for
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.LanguageGroup"/> 1 counters during font processing.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.#ctor(UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder)">
            <summary>
            Creates a new <see cref="T:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary"/>.
            </summary>
            <param name="builder">The builder used to gather property values.</param>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder">
            <summary>
            A mutable builder which can set any property of the private dictionary and performs no validation.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.BlueValues">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueValues"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.OtherBlues">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.OtherBlues"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.FamilyBlues">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.FamilyBlues"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.FamilyOtherBlues">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.FamilyOtherBlues"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.BlueScale">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueScale"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.BlueShift">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueShift"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.BlueFuzz">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BlueFuzz"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.StandardHorizontalWidth">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StandardVerticalWidth"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.StandardVerticalWidth">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StandardVerticalWidth"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.StemSnapHorizontalWidths">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StemSnapHorizontalWidths"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.StemSnapVerticalWidths">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.StemSnapVerticalWidths"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.ForceBold">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.ForceBold"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.LanguageGroup">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.LanguageGroup"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.BaseBuilder.ExpansionFactor">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.ExpansionFactor"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CharStringStack">
            <summary>
            The stack of numeric operands currently active in a CharString.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CharStringStack.Length">
            <summary>
            The current size of the stack.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CharStringStack.CanPop">
            <summary>
            Whether it's possible to pop a value from either end of the stack.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CharStringStack.PopTop">
            <summary>
            Remove and return the value from the top of the stack.
            </summary>
            <returns>The value from the top of the stack.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CharStringStack.PopBottom">
            <summary>
            Remove and return the value from the bottom of the stack.
            </summary>
            <returns>The value from the bottom of the stack.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CharStringStack.Push(System.Double)">
            <summary>
            Adds the value to the top of the stack.
            </summary>
            <param name="value">The value to add.</param>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CharStringStack.Clear">
            <summary>
            Removes all values from the stack.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatEmptyCharset">
            <summary>
            An empty Charset for CID fonts which map from Character Id to Glyph Id without using strings.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatExpertCharset">
            <summary>
            A predefined Charset for a Compact Font Format font with Charset Id of 1.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatExpertSubsetCharset">
            <summary>
            A predefined Charset for a Compact Font Format font with Charset Id of 2.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatFormat0Charset">
            <summary>
            A Charset from a Compact Font Format font file best for fonts with relatively unordered string ids.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatFormat1Charset">
            <summary>
            A Charset from a Compact Font Format font file best for fonts with well ordered string ids.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatFormat2Charset">
            <summary>
            A Charset from a Compact Font Format font file best for fonts with a large number of well ordered string ids.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Charsets.CompactFontFormatIsoAdobeCharset">
            <summary>
            A predefined Charset for a Compact Font Format font with Charset Id of 0.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.LazyType2Command">
            <summary>
            Represents the deferred execution of a Type 2 charstring command.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.LazyType2Command.Name">
            <summary>
            The name of the command to run. See the Type 2 charstring specification for the possible command names.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.LazyType2Command.#ctor(System.String,System.Int32,System.Action{UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.LazyType2Command"/>.
            </summary>
            <param name="name">The name of the command.</param>
            <param name="minimumStackParameters">Minimum number of argument which must be on the stack or -1 if no checking</param>
            <param name="runCommand">The action to execute when evaluating the command. This modifies the <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext"/>.</param>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.LazyType2Command.Run(UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext)">
            <summary>
            Evaluate the command.
            </summary>
            <param name="context">The current <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext"/>.</param>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext">
            <summary>
            The context used and updated when interpreting the commands for a charstring.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext.Stack">
            <summary>
            The numbers currently on the Type 2 Build Char stack.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext.Path">
            <summary>
            The current path.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext.CurrentLocation">
            <summary>
            The current location of the active point.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext.Width">
            <summary>
             If the charstring has a width other than that of defaultWidthX it must be specified as the first
             number in the charstring, and encoded as the difference from nominalWidthX.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2BuildCharContext.BeforeMoveTo">
            <summary>
            Every character path and subpath must begin with one of the
            moveto operators. If the current path is open when a moveto
            operator is encountered, the path is closed before performing
            the moveto operation.
            <para>See 4.1 Path Construction Operators in 'The Type 2 Charstring Format, Technical Note #5177', 16 March 2000</para>
            <see href="https://adobe-type-tools.github.io/font-tech-notes/pdfs/5177.Type2.pdf"/>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStringParser">
            <summary>
            Decodes the commands and numbers making up a Type 2 CharString. A Type 2 CharString extends on the Type 1 CharString format.
            Compared to the Type 1 format, the Type 2 encoding offers smaller size and an opportunity for better rendering quality and
            performance. The Type 2 charstring operators are (with one exception) a superset of the Type 1 operators.
            </summary>
            <remarks>
            A Type 2 charstring program is a sequence of unsigned 8-bit bytes that encode numbers and operators.
            The byte value specifies a operator, a number, or subsequent bytes that are to be interpreted in a specific manner
            </remarks>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStringParser.InterpretNumber(System.Byte,System.Collections.Generic.IReadOnlyList{System.Byte},System.Int32@)">
            <summary>
            The Type 2 interpretation of a number with an initial byte value of 255 differs from how it is interpreted in the Type 1 format
            and 28 has a special meaning.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings">
            <summary>
            Stores the decoded command sequences for Type 2 CharStrings from a Compact Font Format font as well
            as the local (per font) and global (per font set) subroutines.
            The CharStrings are lazily evaluated.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings.CharStrings">
            <summary>
            The decoded charstrings in this font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings.Generate(System.String,System.Double,System.Double)">
            <summary>
            Evaluate the CharString for the character with a given name returning the path constructed for the glyph.
            </summary>
            <param name="name">The name of the character to retrieve the CharString for.</param>
            <param name="defaultWidthX">The default width for the glyph from the font's private dictionary.</param>
            <param name="nominalWidthX">The nominal width which individual glyph widths are encoded as the difference from.</param>
            <returns>A <see cref="T:UglyToad.PdfPig.Core.PdfSubpath"/> for the glyph.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings.CommandSequence.#ctor(System.Collections.Generic.IReadOnlyList{System.Single},System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings.CommandSequence.CommandIdentifier})">
            <summary>
            The ordered list of numbers and commands for a Type 2 charstring or subroutine.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2CharStrings.CommandSequence.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2Glyph">
            <summary>
            Since Type 2 CharStrings may define their width as the first argument (as a delta from the font's nominal width X)
            we can retrieve both details for the Type 2 glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2Glyph.Path">
            <summary>
            The path of the glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2Glyph.Width">
            <summary>
            The width of the glyph as a difference from the nominal width X for the font. Optional.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2Glyph.#ctor(System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Core.PdfSubpath},System.Nullable{System.Double})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CharStrings.Type2Glyph"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatBaseEncoding.GetName(System.Int32)">
            <summary>
            Returns the PostScript name of the glyph for the given character code.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData">
            <summary>
            Provides access to the raw bytes of this Compact Font Format file with utility methods for reading data types from it.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.Position">
            <summary>
            The current position in the data.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.Length">
            <summary>
            The length of the data.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.#ctor(System.Collections.Generic.IReadOnlyList{System.Byte})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadString(System.Int32,System.Text.Encoding)">
            <summary>
            Read a string of the specified length.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadCard8">
            <summary>
            Read Card8 format.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadCard16">
            <summary>
            Read Card16 format.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadOffsize">
            <summary>
            Read Offsize.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadOffset(System.Int32)">
            <summary>
            Read Offset.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadByte">
            <summary>
            Read byte.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.Peek">
            <summary>
            Peek the next byte without advancing the data.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.CanRead">
            <summary>
            Whether there's more data to read in the input.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.Seek(System.Int32)">
            <summary>
            Move to the given offset from the beginning.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadLong">
            <summary>
            Read long.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadSid">
            <summary>
            Read sid.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.ReadBytes(System.Int32)">
            <summary>
            Read byte array of given length.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData.SnapshotPortion(System.Int32,System.Int32)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData"/> from this data with a snapshot at the position and length.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatExpertEncoding.ExpertEncodingTable">
            <summary>
            Table of character codes and their corresponding sid.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont">
            <summary>
            A Compact Font Format (CFF) font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.Encoding">
            <summary>
            The encoding for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.FontMatrix">
            <summary>
            The font matrix for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.Weight">
            <summary>
            The value of Weight from the top dictionary or <see langword="null"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.ItalicAngle">
            <summary>
            The value of Italic Angle from the top dictionary or 0.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.GetCharacterBoundingBox(System.String)">
            <summary>
            Get the bounding box for the character with the given name.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.TryGetPath(System.String,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Core.PdfSubpath}@)">
            <summary>
            Get the pdfpath for the character with the given name.
            </summary>
            <param name="characterName"></param>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.GetCharacterPath(System.String)">
            <summary>
            GetCharacterPath
            </summary>
            <param name="characterName"></param>
            <returns></returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.GetDefaultWidthX(System.String)">
            <summary>
            Get the default width of x for the character.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont.GetNominalWidthX(System.String)">
            <summary>
            Get the nominal width of x for the character.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection">
            <summary>
            A Compact Font Format (CFF) font program as described in The Compact Font Format specification (Adobe Technical Note #5176).
            A CFF font may contain multiple fonts and achieves compression by sharing details between fonts in the set.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.Header">
            <summary>
            The decoded header table for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.Fonts">
            <summary>
            The individual fonts contained in this font keyed by name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.FirstFont">
            <summary>
            The first font contained in the collection.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.#ctor(UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader,System.Collections.Generic.IReadOnlyDictionary{System.String,UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFont})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection"/>.
            </summary>
            <param name="header">The header table for the font.</param>
            <param name="fontSet">The fonts in this font program.</param>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.GetFirstTransformationMatrix">
            <summary>
            Get the first font matrix in the font collection.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.GetCharacterBoundingBox(System.String)">
            <summary>
            Get the bounding box for a character if the font contains a corresponding glyph.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatFontCollection.GetCharacterName(System.Int32)">
            <summary>
            Get the name for the character with the given character code from the font.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader">
            <summary>
            The header table for the binary data of a Compact Font Format file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.MajorVersion">
            <summary>
            The major version of this font format. Starting at 1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.MinorVersion">
            <summary>
            The minor version of this font format. Starting at 0. Indicates extensions to the format which
            are undetectable by readers which do not support them.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.SizeInBytes">
            <summary>
            Indicates the size of this header in bytes so that future changes to the format may include extra data after the <see cref="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.OffsetSize"/> field.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.OffsetSize">
            <summary>
            Specifies the size of all offsets relative to the start of the data in the font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.#ctor(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader"/>.
            </summary>
            <param name="majorVersion">The major version of this font format.</param>
            <param name="minorVersion">The minor version of this font format.</param>
            <param name="sizeInBytes">Indicates the size of this header in bytes so that future changes to the format may include extra data after the offsetSize field.</param>
            <param name="offsetSize">Specifies the size of all offsets relative to the start of the data in the font.</param>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatHeader.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatParser">
            <summary>
            Parse the Compact Font Format (CFF).
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatParser.Parse(UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData)">
            <summary>
            Read the Compact Font Format font from the input data.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatParser.ReadStringIndex(UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatData)">
            <summary>
            Reads indexed string data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.CompactFontFormat.CompactFontFormatStandardEncoding.CharacterCodeToSid">
            <summary>
            Table of character codes and their corresponding sid.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.InitialRandomSeed">
            <summary>
            Compatibility entry.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.LocalSubroutineOffset">
            <summary>
            The offset in bytes for the local subroutine index in this font. The value is relative to this private dictionary.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.DefaultWidthX">
            <summary>
            If a glyph's width equals the default width X it can be omitted from the charstring.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.NominalWidthX">
            <summary>
            If not equal to <see cref="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.DefaultWidthX"/>, Glyph width is computed by adding the charstring width to the nominal width X value.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.#ctor(UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.Builder)">
            <inheritdoc />
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary" />.
            </summary>
            <param name="builder">The builder used to gather property values.</param>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.Builder.DefaultWidthX">
            <summary>
            If a glyph's width equals the default width X it can be omitted from the charstring.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.Builder.NominalWidthX">
            <summary>
            If not equal to <see cref="P:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatPrivateDictionary.Builder.DefaultWidthX"/>, Glyph width is computed by adding the charstring width to the nominal width X value.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatCharStringType">
            <summary>
            Defines the format of the CharString data contained within a Compact Font Format font.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatCharStringType.Type1">
            <summary>
            The Type 1 CharString format as defined by the Adobe Type 1 Font Format.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.CompactFontFormat.Dictionaries.CompactFontFormatCharStringType.Type2">
            <summary>
            The Type 2 CharString format as defined by Adobe Technical Note #5177. This is the default type.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.CorruptCompressedDataException">
            <summary>
            Thrown when a PDF contains an invalid compressed data stream.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CorruptCompressedDataException.#ctor">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CorruptCompressedDataException.#ctor(System.String)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CorruptCompressedDataException.#ctor(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.CorruptCompressedDataException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.BuiltInEncoding">
            <inheritdoc />
            <summary>
            An encoding built in to a TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.BuiltInEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.BuiltInEncoding.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.Int32,System.String})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.Encodings.BuiltInEncoding"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.DifferenceBasedEncoding">
            <inheritdoc />
            <summary>
            Created by combining a base encoding with the differences.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.DifferenceBasedEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.DifferenceBasedEncoding.#ctor(UglyToad.PdfPig.Fonts.Encodings.Encoding,System.Collections.Generic.IReadOnlyList{System.ValueTuple{System.Int32,System.String}})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.Encodings.DifferenceBasedEncoding"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.Encoding">
            <summary>
            Maps character codes to glyph names from a PostScript encoding.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.Encoding.CodeToName">
            <summary>
            Mutable code to name map.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.Encoding.CodeToNameMap">
            <summary>
            Maps from character codes to names.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.Encoding.NameToCode">
            <summary>
            Mutable name to code map.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.Encoding.NameToCodeMap">
            <summary>
            Maps from names to character cocdes.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.Encoding.EncodingName">
            <summary>
            The name of this encoding.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.ContainsName(System.String)">
            <summary>
            Whether this encoding contains a code for the name.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.ContainsCode(System.Int32)">
            <summary>
            Whether this encoding contains a name for the code.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.GetName(System.Int32)">
            <summary>
            Get the character name corresponding to the given code.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.GetCode(System.String)">
            <summary>
            Get the character code from name 
            </summary>
            <param name="name">Character name (eg. euro, ampersand, A, space)</param>
            <returns>-1 if not found otherwise the character code</returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.Add(System.Int32,System.String)">
            <summary>
            Add a character code and name pair.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.Encoding.TryGetNamedEncoding(UglyToad.PdfPig.Tokens.NameToken,UglyToad.PdfPig.Fonts.Encodings.Encoding@)">
            <summary>
            Get a known encoding instance with the given name.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.MacExpertEncoding.EncodingTable">
            <summary>
            Table of octal character codes and their corresponding names.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.MacOsRomanEncoding">
            <inheritdoc />
            <summary>
            Similar to the <see cref="T:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding" /> with 15 additional entries.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.MacOsRomanEncoding.Instance">
            <summary>
            The single instance of this encoding.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding">
            <summary>
            The Mac Roman encoding.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding.EncodingTable">
            <summary>
            Table of octal character codes and their corresponding names.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding.Instance">
            <summary>
            The single instance of this encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding.#ctor">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.Encodings.MacRomanEncoding"/>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.StandardEncoding">
            <summary>
            The standard PDF encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.StandardEncoding.Instance">
            <summary>
            The single instance of the standard encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.StandardEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.SymbolEncoding">
            <summary>
            Symbol encoding.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.SymbolEncoding.EncodingTable">
            <summary>
            EncodingTable for Symbol
            PDF Spec 1.7 Page 1013 https://opensource.adobe.com/dc-acrobat-sdk-docs/pdfstandards/pdfreference1.7old.pdf#page1013
            Note spec has code values as octal (base 8) with leading zero (supported in 'C' and 'Java') but not by C#
            Code values are already converted to base 10 prior to compile.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.SymbolEncoding.Instance">
            <summary>
            Single instance of this encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.SymbolEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.WinAnsiEncoding">
            <summary>
            Windows ANSI encoding.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.WinAnsiEncoding.EncodingTable">
            <summary>
            The encoding table is taken from the Appendix of the specification.
            These codes are in octal.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.WinAnsiEncoding.Instance">
            <summary>
            Single instance of this encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.WinAnsiEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Encodings.ZapfDingbatsEncoding">
            <summary>
            Zapf Dingbats encoding.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Encodings.ZapfDingbatsEncoding.EncodingTable">
            <summary>
            EncodingTable for ZapfDingbats
            PDF Spec 1.7 Page 1016 https://opensource.adobe.com/dc-acrobat-sdk-docs/pdfstandards/pdfreference1.7old.pdf#page1016
            Note spec has code values are octal (base 8) with leading zero (supported in 'C' and 'Java') but not by C#
            Code values are already converted to base 10 prior to compile. Original octal values in comments on each line.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.ZapfDingbatsEncoding.Instance">
            <summary>
            Single instance of this encoding.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Encodings.ZapfDingbatsEncoding.EncodingName">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.GlyphList">
            <summary>
            A list which maps PostScript glyph names to unicode values.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.GlyphList.AdobeGlyphList">
            <summary>
            The Adobe Glyph List.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.GlyphList.AdditionalGlyphList">
            <summary>
            An extension to the Adobe Glyph List.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.GlyphList.ZapfDingbats">
            <summary>
            Zapf Dingbats.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.GlyphList.UnicodeCodePointToName(System.Int32)">
            <summary>
            Get the name for the unicode code point value.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.GlyphList.NameToUnicode(System.String)">
            <summary>
            Get the unicode value for the glyph name.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.InvalidFontFormatException">
            <summary>
            The exception thrown when an error is encountered parsing a font from the PDF document.
            This occurs where the format of the font program or dictionary does not meet the specification.
            </summary>
            <inheritdoc cref="T:System.Exception"/>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.InvalidFontFormatException.#ctor">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.InvalidFontFormatException.#ctor(System.String)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.InvalidFontFormatException.#ctor(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.InvalidFontFormatException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14">
            <summary>
            There are 14 special Type 1 fonts which must be available. The list of PostScript names for these is given in the remarks section.
            A font dictionary for a standard 14 font may omit the FirstChar, LastChar, Widths and FontDescriptor entries.
            The metrics for these fonts are provided by the Adobe Font Metrics (AFM) files.
            </summary>
            <remarks>
            Standard 14 fonts are deprecated as of PDF 1.5+ however support is still required for backwards compatibility.<br/>
            The standard 14 are:<br/>
            Times−Roman<br/>
            Helvetica<br/>
            Courier<br/>
            Symbol<br/>
            Times−Bold<br/>
            Helvetica−Bold<br/>
            Courier−Bold<br/>
            ZapfDingbats<br/>
            Times−Italic<br/>
            Helvetica−Oblique<br/>
            Courier−Oblique<br/>
            Times−BoldItalic<br/>
            Helvetica−BoldOblique<br/>
            Courier−BoldOblique
            </remarks>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14.GetAdobeFontMetrics(System.String)">
            <summary>
            Get the Adobe Font Metrics as <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics"/> for a font.
            If the font is not found this returns <see langword="null"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14.GetAdobeFontMetrics(UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font)">
            <summary>
            Get the Adobe Font Metrics as <see cref="T:UglyToad.PdfPig.Fonts.AdobeFontMetrics.AdobeFontMetrics"/> for a Standard14 font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14.IsFontInStandard14(System.String)">
            <summary>
            Determines if a font with this name is a standard 14 font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14.GetNames">
            <summary>
            Returns the set of Standard 14 font names, including additional names.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14.GetMappedFontName(System.String)">
            <summary>
            Get the official Standard 14 name of the actual font which the given font name maps to.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font">
            <summary>
            The Standard 14 fonts included by default in PDF readers.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.TimesRoman">
            <summary>
            Times New Roman.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.TimesBold">
            <summary>
            Times New Roman Bold. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.TimesItalic">
            <summary>
            Times New Roman Italic. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.TimesBoldItalic">
            <summary>
            Times New Roman Bold and Italic. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.Helvetica">
            <summary>
            Helvetica.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.HelveticaBold">
            <summary>
            Helvetica Bold. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.HelveticaOblique">
            <summary>
            Helvetica Oblique (Italic without different font shapes). 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.HelveticaBoldOblique">
            <summary>
            Helvetica Bold and Oblique. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.Courier">
            <summary>
            Courier. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.CourierBold">
            <summary>
            Courier Bold. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.CourierOblique">
            <summary>
            Courier Oblique.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.CourierBoldOblique">
            <summary>
            Courier Bold and Oblique.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.Symbol">
            <summary>
            Symbol. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Standard14Fonts.Standard14Font.ZapfDingbats">
            <summary>
            Zapf Dingbats. 
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.SystemFonts.ISystemFontFinder">
            <summary>
            Used to find named fonts from the host operating/file system.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.SystemFonts.ISystemFontFinder.GetTrueTypeFont(System.String)">
            <summary>
            Get the TrueType font with the specified name.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder">
            <inheritdoc />
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder.Instance">
            <summary>
            The instance of <see cref="T:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder.#ctor">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.SystemFonts.SystemFontFinder.GetTrueTypeFont(System.String)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags">
            <summary>
            Specifies the meaning of the transformation entries for a composite glyph definition.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.Args1And2AreWords">
            <summary>
            If set arguments are words, otherwise they are bytes.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.ArgsAreXAndYValues">
            <summary>
            If set arguments are x y offset values, otherwise they are points.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.RoundXAndYToGrid">
            <summary>
            If arguments are x y offset values and this is set then the values are rounded to the closest grid lines before addition to the glyph.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.WeHaveAScale">
            <summary>
            If set the scale value is read in 2.14 format (between -2 to &lt; 2) and the glyph is scaled before grid-fitting. Otherwise scale is 1.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.Reserved">
            <summary>
            Reserved for future use, should be set to 0.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.MoreComponents">
            <summary>
            Indicates that there is a glyph following the current one.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.WeHaveAnXAndYScale">
            <summary>
            Indicates that X is scaled differently to Y.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.WeHaveATwoByTwo">
            <summary>
            Indicates that there is a 2 by 2 transformation used to scale the component.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.WeHaveInstructions">
            <summary>
            Indicates that there are instructions for the composite character following the last component.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.CompositeGlyphFlags.UseMyMetrics">
            <summary>
            If set this forces advance width and left side bearing for the composite to be equal to those from the original glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Glyphs.Glyph.Bounds">
            <summary>
            The bounding rectangle for the character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Glyphs.Glyph.Instructions">
            <summary>
            The bytes of the instructions for this glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Glyphs.Glyph.EndPointsOfContours">
            <summary>
            An array of the last points of each contour.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Glyphs.Glyph.midValue(UglyToad.PdfPig.Fonts.TrueType.Glyphs.GlyphPoint,UglyToad.PdfPig.Fonts.TrueType.Glyphs.GlyphPoint)">
            <summary>
            This creates an onCurve point that is between point1 and point2.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric">
            <summary>
            The pair of horizontal metrics for an individual glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric.AdvanceWidth">
            <summary>
            The advance width.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric.LeftSideBearing">
            <summary>
            The left side bearing.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric.#ctor(System.UInt16,System.Int16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags">
            <summary>
            Specifies the meaning of each coordinate in the simple glyph definition.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.OnCurve">
            <summary>
            The point is on the curve.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.XSingleByte">
            <summary>
            The x-coordinate is 1 byte long instead of 2.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.YSingleByte">
            <summary>
            The y-coordinate is 1 byte long instead of 2.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.Repeat">
            <summary>
            The next byte specifies the number of times to repeat this set of flags.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.ThisXIsTheSame">
            <summary>
            If <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.XSingleByte"/> is set this means the sign of the x-coordinate is positive.
            If <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.XSingleByte"/> is not set then the current x-coordinate is the same as the previous.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.ThisYIsTheSame">
            <summary>
            If <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.YSingleByte"/> is set this means the sign of the y-coordinate is positive.
            If <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Glyphs.SimpleGlyphFlags.YSingleByte"/> is not set then the current y-coordinate is the same as the previous.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier">
            <summary>
            The meaning of the platform specific encoding identifier when the <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier"/> is <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Macintosh"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Roman">
            <summary>
            Roman.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Japanese">
            <summary>
            Japanese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.ChineseTraditional">
            <summary>
            Traditional Chinese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Korean">
            <summary>
            Korean.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Arabic">
            <summary>
            Arabic.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Hebrew">
            <summary>
            Hebrew.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Greek">
            <summary>
            Greek.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Russian">
            <summary>
            Russian.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.RSymbol">
            <summary>
            RSymbol.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Devanagari">
            <summary>
            Devanagari.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Gurmukhi">
            <summary>
            Gurmukhi.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Gujarati">
            <summary>
            Gujarati.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Oriya">
            <summary>
            Oriya.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Bengali">
            <summary>
            Bengali.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Tamil">
            <summary>
            Tamil.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Telugu">
            <summary>
            Telugu.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Kannada">
            <summary>
            Kannada.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Malayalam">
            <summary>
            Malayalam.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Sinhalese">
            <summary>
            Sinhalese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Burmese">
            <summary>
            Burmese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Khmer">
            <summary>
            Khmer.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Thai">
            <summary>
            Thai.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Laotian">
            <summary>
            Laotian.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Georgian">
            <summary>
            Georgian.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Armenian">
            <summary>
            Armenian.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.ChineseSimplified">
            <summary>
            Simplified Chinese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Tibetan">
            <summary>
            Tibetan.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Mongolian">
            <summary>
            Mongolian.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Geez">
            <summary>
            Ge'ez.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Slavic">
            <summary>
            Slavic.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Vietnamese">
            <summary>
            Vietnamese.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Sindhi">
            <summary>
            Sindhi.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshEncodingIdentifier.Uninterpreted">
            <summary>
            Uninterpreted.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier">
            <summary>
            Uniquely defines the language in which the name character string is written
            for a name record in a name table.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.English">
            <summary>English.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.French">
            <summary>French.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.German">
            <summary>German.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Italian">
            <summary>Italian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Dutch">
            <summary>Dutch.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Swedish">
            <summary>Swedish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Spanish">
            <summary>Spanish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Danish">
            <summary>Danish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Portuguese">
            <summary>Portuguese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Norwegian">
            <summary>Norwegian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Hebrew">
            <summary>Hebrew.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Japanese">
            <summary>Japanese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Arabic">
            <summary>Arabic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Finnish">
            <summary>Finnish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Greek">
            <summary>Greek.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Icelandic">
            <summary>Icelandic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Maltese">
            <summary>Maltese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Turkish">
            <summary>Turkish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Croatian">
            <summary>Croatian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.ChineseTraditional">
            <summary>Traditional Chinese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Urdu">
            <summary>Urdu.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Hindi">
            <summary>Hindi.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Thai">
            <summary>Thai.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Korean">
            <summary>Korean.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Lithuanian">
            <summary>Lithuanian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Polish">
            <summary>Polish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Hungarian">
            <summary>Hungarian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Estonian">
            <summary>Estonian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Latvian">
            <summary>Latvian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Sami">
            <summary>Sami.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Faroese">
            <summary>Faroese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.FarsiPersian">
            <summary>Farsi (Persian).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Russian">
            <summary>Russian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.ChineseSimplified">
            <summary>Simplified Chinese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Flemish">
            <summary>Flemish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.IrishGaelic">
            <summary>Irish Gaelic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Albanian">
            <summary>Albanian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Romanian">
            <summary>Romanian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Czech">
            <summary>Czech.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Slovak">
            <summary>Slovak.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Slovenian">
            <summary>Slovenian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Yiddish">
            <summary>Yiddish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Serbian">
            <summary>Serbian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Macedonian">
            <summary>Macedonian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Bulgarian">
            <summary>Bulgarian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Ukrainian">
            <summary>Ukrainian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Byelorussian">
            <summary>Byelorussian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Uzbek">
            <summary>Uzbek.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Kazakh">
            <summary>Kazakh.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.AzerbaijaniCyrillic">
            <summary>Azerbaijani (Cyrillic).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.AzerbaijaniArabic">
            <summary>Azerbaijani (Arabic).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Armenian">
            <summary>Armenian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Georgian">
            <summary>Georgian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Moldavian">
            <summary>Moldavian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Kirghiz">
            <summary>Kirghiz.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tajiki">
            <summary>Tajiki.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Turkmen">
            <summary>Turkmen.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Mongolian">
            <summary>Mongolian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.MongolianCyrillic">
            <summary>Mongolian (Cyrillic).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Pashto">
            <summary>Pashto.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Kurdish">
            <summary>Kurdish.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Kashmiri">
            <summary>Kashmiri.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Sindhi">
            <summary>Sindhi.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tibetan">
            <summary>Tibetan.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Nepali">
            <summary>Nepali.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Sanskrit">
            <summary>Sanskrit.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Marathi">
            <summary>Marathi.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Bengali">
            <summary>Bengali.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Assamese">
            <summary>Assamese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Gujarati">
            <summary>Gujarati.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Punjabi">
            <summary>Punjabi.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Oriya">
            <summary>Oriya.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Malayalam">
            <summary>Malayalam.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Kannada">
            <summary>Kannada.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tamil">
            <summary>Tamil.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Telugu">
            <summary>Telugu.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Sinhalese">
            <summary>Sinhalese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Burmese">
            <summary>Burmese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Khmer">
            <summary>Khmer.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Lao">
            <summary>Lao.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Vietnamese">
            <summary>Vietnamese.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Indonesian">
            <summary>Indonesian.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tagalog">
            <summary>Tagalog.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.MalayRoman">
            <summary>Malay (Roman).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.MalayArabic">
            <summary>Malay (Arabic).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Amharic">
            <summary>Amharic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tigrinya">
            <summary>Tigrinya.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Galla">
            <summary>Galla.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Somali">
            <summary>Somali.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Swahili">
            <summary>Swahili.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.KinyarwandaRuanda">
            <summary>Kinyarwanda Ruanda.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Rundi">
            <summary>Rundi.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.NyanjaChewa">
            <summary>NyanjaChewa.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Malagasy">
            <summary>Malagasy.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Esperanto">
            <summary>Esperanto.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Welsh">
            <summary>Welsh.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Basque">
            <summary>Basque.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Catalan">
            <summary>Catalan.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Latin">
            <summary>Latin.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Quechua">
            <summary>Quechua.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Guarani">
            <summary>Guarani.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Aymara">
            <summary>Aymara.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tatar">
            <summary>Tatar.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Uighur">
            <summary>Uighur.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Dzongkha">
            <summary>Dzongkha.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.JavaneseRoman">
            <summary>Javanese (Roman).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.SundaneseRoman">
            <summary>Sundanese (Roman).</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Galician">
            <summary>Galician.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Afrikaans">
            <summary>Afrikaans.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Breton">
            <summary>Breton.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Inuktitut">
            <summary>Inuktitut.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.ScottishGaelic">
            <summary>Scottish Gaelic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.ManxGaelic">
            <summary>Manx Gaelic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.IrishGaelicWithDotAbove">
            <summary>Irish Gaelic With Dot Above.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Tongan">
            <summary>Tongan.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.GreekPolytonic">
            <summary>Greek Polytonic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.Greenlandic">
            <summary>Greenlandic.</summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeMacintoshLanguageIdentifier.AzerbaijaniRoman">
            <summary>Azerbaijani (Roman).</summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord">
            <summary>
            A record in a TrueType font which is a human-readable name for
            a feature, setting, copyright notice, font name or other font related
            information.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.PlatformId">
            <summary>
            The supported platform identifier.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.PlatformEncodingId">
            <summary>
            The platform specific encoding id. Interpretation depends on the value of the <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.PlatformId"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.LanguageId">
            <summary>
            The language id uniquely defines the language in which the string is written for this record.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.NameId">
            <summary>
            Used to reference this record by other tables in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.Value">
            <summary>
            The value of this record.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.#ctor(UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier,System.UInt16,System.UInt16,System.UInt16,System.String)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier">
            <summary>
            The platform identifier for a TrueType/OpenType font allows for platform specific implementations.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Unicode">
            <summary>
            Unicode
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Macintosh">
            <summary>
            Macintosh.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Iso">
            <summary>
            The platform identifier 2 was originally to use with ISO 10646, but is now deprecated, as it and Unicode have identical character code assignments.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Windows">
            <summary>
            Microsoft Windows.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier">
            <summary>
            The meaning of the platform specific encoding when the <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier"/> is <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Unicode"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.Default">
            <summary>
            Default semantics.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.Version1Point1">
            <summary>
            Version 1.1 semantics.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.Iso10646">
            <summary>
            ISO 10646 1993 semantics (deprecated).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.Unicode2BmpOnly">
            <summary>
            Unicode 2.0 and above semantics for BMP characters only.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.Unicode2NonBmpAllowed">
            <summary>
            Uncidoe 2.0 and above semantics including non-BMP characters.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.UnicodeVariationSequences">
            <summary>
            Unicode Variation Sequences.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeUnicodeEncodingIndentifier.FullUnicode">
            <summary>
            Full Unicode coverage.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier">
            <summary>
            The meaning of the platform specific encoding when the <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier"/> is <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypePlatformIdentifier.Windows"/>.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Symbol">
            <summary>
            Symbol.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.UnicodeBmp">
            <summary>
            Unicode BMP.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.ShiftJis">
            <summary>
            ShiftJIS.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Prc">
            <summary>
            PRC.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Big5">
            <summary>
            Big5.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Wansung">
            <summary>
            Wansung.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Johab">
            <summary>
            Johab.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Reserved7">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Reserved8">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.Reserved9">
            <summary>
            Reserved.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeWindowsEncodingIdentifier.FullUnicode">
            <summary>
            Unicode full repertoire.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister">
            <summary>
            The set of tables in a TrueType font interpreted by the library.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.HeaderTable">
            <summary>
            This table contains global information about the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.GlyphTable">
            <summary>
            This table contains the data that defines the appearance of the glyphs in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.HorizontalHeaderTable">
            <summary>
            This table contains information needed to layout fonts whose characters are written horizontally.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.HorizontalMetricsTable">
            <summary>
            This table contains metric information for the horizontal layout each of the glyphs in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.IndexToLocationTable">
            <summary>
            This table stores the offsets to the locations of the glyphs (relative to the glyph table).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.MaximumProfileTable">
            <summary>
            This table establishes the memory requirements for the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.NameTable">
            <summary>
            This table defines strings used by the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.PostScriptTable">
            <summary>
            This table contains information needed to use a TrueType font on a PostScript printer. 
            It contains the PostScript names for all of the glyphs in the font
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.CMapTable">
            <summary>
            Defines mapping of character codes to glyph index values in the font.
            Can contain multiple sub-tables to support multiple encoding schemes.
            Where a character code isn't found it should map to index 0.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.Os2Table">
            <summary>
            This table consists of a set of metrics that are required by Windows.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.#ctor(UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.Builder)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister"/>.
            </summary>
            <param name="builder">The builder with necessary tables set.</param>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.Builder">
            <summary>
            Used to gather the necessary tables for a TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.Builder.CMapTable">
            <summary>
            Defines mapping of character codes to glyph index values in the font.
            Can contain multiple sub-tables to support multiple encoding schemes.
            Where a character code isn't found it should map to index 0.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Parser.TrueTypeFontParser">
            <summary>
            Parses TrueType fonts.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Parser.TrueTypeFontParser.Parse(UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes)">
            <summary>
            Parse the font from the input data.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter">
            <summary>
            Produces a glyph table which contains a subset of the glyphs in the input font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter.SubsetGlyphTable(UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont,System.Byte[],UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex[])">
            <summary>
            Creates a new glyph table from the input font which contains only the glyphs required by the input mapping.
            </summary>
            <param name="font">The font used to create this subset.</param>
            <param name="fontBytes">The raw bytes of the input font.</param>
            <param name="mapping">The mapping of old glyph indices to new glyph indices.</param>
            <returns>A new glyph table and associated information for use in creating a valid TrueType font file.</returns>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter.GlyphRecord.DependencyIndices">
            <summary>
            Indices of any glyphs this glyph depends on, if it's a composite glyph.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter.CompositeGlyphIndexReference">
            <summary>
            Marks a glyph index referenced by a composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter.CompositeGlyphIndexReference.Index">
            <summary>
            The index of the glyph reference by this composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeGlyphTableSubsetter.CompositeGlyphIndexReference.OffsetOfIndexWithinData">
            <summary>
            The offset of the index value in the data which this composite glyph was read from.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetEncoding">
            <summary>
            A new encoding to create for the subsetted TrueType file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetEncoding.Characters">
            <summary>
            The characters to include in the subset in order where index is the character code.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetEncoding.#ctor(System.Collections.Generic.IReadOnlyList{System.Char})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetEncoding"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable">
            <summary>
            Details of the new glyph 'glyf' table created when subsetting a TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.Bytes">
            <summary>
            The raw bytes of the new table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.GlyphOffsets">
            <summary>
            The offsets of each of the glyphs in the new table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.HorizontalMetrics">
            <summary>
            The corresponding horizontal metrics for each glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.GlyphCount">
            <summary>
            The number of glyphs in the new table.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.#ctor(System.Byte[],System.UInt32[],UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.OffsetsAsLongs">
            <summary>
            Convert the <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.GlyphOffsets"/> values to <see langword="long"/>s.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetGlyphTable.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter">
            <summary>
            Generate a subset of a TrueType font file containing only the required glyphs.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OptionalTags">
            <summary>
            The tags for tables which are optional or required but can be skipped for PDF. 
            This should also include hdmx, kern and OS/2 tables.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.Subset(System.Byte[],UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetEncoding)">
            <summary>
            Generate a subset of the input font containing only the data required for the glyphs specified in the encoding.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex">
            <summary>
            Mapping from a glyph index in the old file to the new (subset) glyph index.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex.OldIndex">
            <summary>
            Glyph index in the old input file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex.NewIndex">
            <summary>
            Glyph index in the new subset file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex.Represents">
            <summary>
            The character represented by this mapping.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex.#ctor(System.UInt16,System.UInt16,System.Char)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Subsetting.TrueTypeSubsetter.OldToNewGlyphIndex.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ByteEncodingCMapTable">
            <summary>
            The format 0 sub-table where character codes and glyph indices are restricted to a single bytes.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable">
            <inheritdoc />
            <summary>
            A format 4 CMap sub-table which defines gappy ranges of character code to glyph index mappings.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform,System.UInt16,System.UInt16,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment},System.Collections.Generic.IReadOnlyList{System.UInt16})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment">
            <summary>
            A contiguous segment which maps character to glyph codes in a Format 4 CMap sub-table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment.StartCode">
            <summary>
            The start character code in the range.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment.EndCode">
            <summary>
            The end character code in the range.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment.IdDelta">
            <summary>
            The delta for the codes in the segment.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment.IdRangeOffset">
            <summary>
            Offset in bytes to glyph index array.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.Format4CMapTable.Segment"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.HighByteMappingCMapTable">
            <inheritdoc />
            <summary>
            A format 2 sub-table for Chinese, Japanese and Korean characters.
            Contains mixed 8/16 bit encodings.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.HighByteMappingCMapTable.SubHeader.FirstCode">
            <summary>
            First valid low byte for the sub header.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.HighByteMappingCMapTable.SubHeader.EntryCount">
            <summary>
            Number of valid low bytes for the sub header.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.HighByteMappingCMapTable.SubHeader.IdDelta">
            <summary>
            Adds to the value from the sub array to provide the glyph index.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.HighByteMappingCMapTable.SubHeader.IdRangeOffset">
            <summary>
            The number of bytes past the actual location of this value where the glyph index array element starts.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable">
            <summary>
            In a TrueType font the CMap table maps from character codes to glyph indices
            A font which can run on multiple platforms will have multiple encoding tables. These are stored as multiple
            sub-tables. The <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable"/> represents a single subtotal.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable.PlatformId">
            <summary>
            The platform identifier.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable.EncodingId">
            <summary>
            Platform specific encoding indentifier. Interpretation depends on the value of the <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable.PlatformId"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable.CharacterCodeToGlyphIndex(System.Int32)">
            <summary>
            Maps from a character code to the array index of the glyph in the font data.
            </summary>
            <param name="characterCode">The character code.</param>
            <returns>The index of the glyph information for this character.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrimmedTableMappingCMapTable">
            <summary>
            A format 6 CMap sub-table which uses 2 bytes to map a contiguous range of character codes to glyph indices.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrimmedTableMappingCMapTable.PlatformId">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrimmedTableMappingCMapTable.EncodingId">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrimmedTableMappingCMapTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform,System.UInt16,System.Int32,System.Int32,System.UInt16[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrimmedTableMappingCMapTable"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform">
            <summary>
            The platform identifier for a CMap table.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform.Unicode">
            <summary>
            Unicode.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform.Macintosh">
            <summary>
            Apple Macintosh.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform.Reserved2">
            <summary>
            Unused.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.TrueTypeCMapPlatform.Windows">
            <summary>
            Microsoft Windows.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable">
            <inheritdoc cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable"/>.
            <summary>
            The cmap table maps character codes to glyph indices.
            The choice of encoding for a particular font is dependent on the conventions used by the intended platform.
            The cmap table can contain multiple encoding tables for use on different platforms, one for each supported encoding scheme.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.Version">
            <summary>
            Version number (0).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.SubTables">
            <summary>
            The sub-tables, one for each supported encoding scheme and platform.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.#ctor(System.UInt16,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Fonts.TrueType.Tables.CMapSubTables.ICMapSubTable})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.TryGetGlyphIndex(System.Int32,System.Int32@)">
            <summary>
            Get the glyph index for the corresponding character code.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.CMapTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable">
            <inheritdoc />
            <summary>
            The 'glyf' table contains the data that defines the appearance of the glyphs in the font. 
            This includes specification of the points that describe the contours that make up a glyph outline and the instructions that grid-fit that glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable.TemporaryCompositeLocation">
            <summary>
            Stores the composite glyph information we read when initially scanning the glyph table.
            Once we have all composite glyphs we can start building them from simple glyphs.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable.TemporaryCompositeLocation.Position">
            <summary>
            Stores the position after reading the contour count and bounds.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable">
            <inheritdoc />
            <summary>
            The 'head' table contains global information about the font. 
            It contains things like as the font version number, the creation and modification dates, revision number and basic typographic data that applies to the font as a whole.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Version">
            <summary>
            Version number.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Revision">
            <summary>
            Revision.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.CheckSumAdjustment">
            <summary>
            Checksum adjustment is used to derive the checksum of the entire TrueType file.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.MagicNumber">
            <summary>
            0x5F0F3CF5.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Flags">
            <summary>
            Flags.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.UnitsPerEm">
            <summary>
            Units per em.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Created">
            <summary>
            Created date.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Modified">
            <summary>
            Modified date.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Bounds">
            <summary>
            Minimum rectangle which contains all glyphs.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.MacStyle">
            <summary>
            MacStyle flags.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.LowestRecommendedPpem">
            <summary>
            Smallest readable size in pixels.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirectionHint">
            <summary>
            Font direction hint.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.IndexToLocFormat">
            <summary>
            0 for short offsets, 1 for long.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.GlyphDataFormat">
            <summary>
            0 for current format.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Single,System.Single,System.UInt32,System.UInt32,System.UInt16,System.UInt16,System.DateTime,System.DateTime,System.Int16,System.Int16,System.Int16,System.Int16,System.UInt16,System.UInt16,System.Int16,UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat,System.Int16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.Load(UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable)">
            <summary>
            Read the header table from the data stream.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection">
            <summary>
            Values of the font direction hint.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection.StronglyRightToLeftWithNeutrals">
            <summary>
            Strongly right to left with neutrals.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection.StronglyRightToLeft">
            <summary>
            Strongly right to left.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection.FullyMixedDirectional">
            <summary>
            Full mixed directional glyphs.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection.StronglyLeftToRight">
            <summary>
            Strongly left to right.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.FontDirection.StronglyLeftToRightWithNeutrals">
            <summary>
            Strongly left to right with neutrals.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle">
            <summary>
            Values of the Mac Style flag in the header table.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.None">
            <summary>
            No flags set.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Bold">
            <summary>
            Bold.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Italic">
            <summary>
            Italic.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Underline">
            <summary>
            Underline.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Outline">
            <summary>
            Outline.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Shadow">
            <summary>
            Shadow.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Condensed">
            <summary>
            Condensed (narrow).
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.HeaderTable.HeaderMacStyle.Extended">
            <summary>
            Extended.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable">
            <inheritdoc />
            <summary>
            The 'hhea' table contains information needed to layout fonts whose characters are written horizontally, that is, either left to right or right to left. 
            This table contains information that is general to the font as a whole.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.MajorVersion">
            <summary>
            Major version number of this table (1).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.MinorVersion">
            <summary>
            Minor version number of this table (0).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.Ascent">
            <summary>
            Distance from baseline to highest ascender.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.Descent">
            <summary>
            Distance from baseline to lower descender.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.LineGap">
            <summary>
            The typographic line gap.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.AdvanceWidthMaximum">
            <summary>
            The maximum advance width value as given by the Horizontal Metrics table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.MinimumLeftSideBearing">
            <summary>
            The minimum left side bearing as given by the Horizontal Metrics table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.MinimumRightSideBearing">
            <summary>
            The minimum right sidebearing.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.XMaxExtent">
            <summary>
            The maximum X extent.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.CaretSlopeRise">
            <summary>
            Used to calculate the slope of the cursor. 1 is vertical.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.CaretSlopeRun">
            <summary>
            0 is vertical.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.CaretOffset">
            <summary>
            The amount by which a slanted highlight on a glyph should be shifted to provide the best appearance. 0 for non-slanted fonts.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.MetricDataFormat">
            <summary>
            0 for the current format.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.NumberOfHeaderMetrics">
            <summary>
            Number of horizontal metrics in the Horizontal Metrics table.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Int32,System.Int32,System.Int16,System.Int16,System.Int16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.UInt16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalHeaderTable"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable">
            <inheritdoc cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable" />
            <summary>
            The 'hmtx' table contains metric information for the horizontal layout each of the glyphs in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.HorizontalMetrics">
            <summary>
            The left-side bearing and advance widths for the glyphs in the font. For a monospace font
            this may only contain a single entry and the left-side bearings will be defined in <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.AdditionalLeftSideBearings"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.AdditionalLeftSideBearings">
            <summary>
            Some fonts may have an array of left side bearings following the <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.HorizontalMetrics"/>. 
            Generally, this array of left side bearings is used for a run of monospaced glyphs. 
            For example, it might be used for a Kanji font or for Courier. 
            The corresponding glyphs are assumed to have the same advance width as that found in the last entry in the <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.HorizontalMetrics"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Fonts.TrueType.Glyphs.HorizontalMetric},System.Collections.Generic.IReadOnlyList{System.Int16})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.GetAdvanceWidth(System.Int32)">
            <summary>
            Get the advance width for a glyph at the given index.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.HorizontalMetricsTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable">
            <inheritdoc cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable"/>
            <summary>
            Stores the offset to the glyph locations relative to the start of the <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.GlyphDataTable"/>.
            Index zero points to the "missing character" which is used for characters not provided by the font.
            The number of glpyhs in this table should match the maximum profile table. The glyph offsets contains
            an extra entry at the last index which points to the end of the glyph data, this makes it possible to compute
            the length of the last glyph entry and supports empty glyphs. 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.Format">
            <summary>
            Indicates the format the offsets were stored in in the underlying file, for <see cref="F:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat.Short"/>
            the values are divided by 2. The values in <see cref="P:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.GlyphOffsets"/> are the real offsets, with any format
            changes removed.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.GlyphOffsets">
            <summary>
            The glyph offsets relative to the start of the glyph data table.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat,System.Collections.Generic.IReadOnlyList{System.UInt32})">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.Load(UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister.Builder)">
            <summary>
            Load the index to location (loca) table from the TrueType font. Requires the maximum profile (maxp) and header (head) table
            to have been parsed.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat">
            <summary>
            The format of glyph offset entries stored in the raw TrueType data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat.Short">
            <summary>
            The actual local offset divided by 2 is stored.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.IndexToLocationTable.EntryFormat.Long">
            <summary>
            The actual local offset is stored.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable">
            <summary>
            A table in a TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable.Tag">
            <summary>
            The tag, a 4 letter/byte code, used to identify this table in a TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable.DirectoryTable">
            <summary>
            The directory entry from the font's offset subtable which indicates the length, offset, type and checksum of a table.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernCoverage">
            <summary>
            The type of kerning covered by this table.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernCoverage.Horizontal">
            <summary>
            The table is horizontal kerning data.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernCoverage.Minimum">
            <summary>
            The table has minimum values rather than kerning values.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernCoverage.CrossStream">
            <summary>
            Kerning is perpendicular to the flow of text.
            If text is horizontal kerning will be in the up/down direction.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernCoverage.Override">
            <summary>
            The value in this sub table should replace the currently accumulated value.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair">
            <summary>
            A kerning value for a pair of glyphs.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair.LeftGlyphIndex">
            <summary>
            The index of the left-hand glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair.RightGlyphIndex">
            <summary>
            The index of the right-hand glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair.Value">
            <summary>
            The kerning value. For values greater than zero the characters are moved apart.
            For values less than zero the characters are moved closer together.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair.#ctor(System.Int32,System.Int32,System.Int16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Kerning.KernPair.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.BasicMaximumProfileTable">
            <summary>
            This table establishes the memory requirements for the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.BasicMaximumProfileTable.Version">
            <summary>
            The table version number. CFF fonts must use version 0.5 and only set number of glyphs. TrueType must use version 1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.BasicMaximumProfileTable.NumberOfGlyphs">
            <summary>
            The number of glyphs in the font.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumPoints">
            <summary>
            Maximum number of points in a non-composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumContours">
            <summary>
            Maximum number of contours in a non-composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumCompositePoints">
            <summary>
            Maximum number of points in a composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumCompositeContours">
            <summary>
            Maximum number of contours in a composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumZones">
            <summary>
            1 if instructions do not use the twilight zone (Z0). 2 if they do. Usually 2.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumTwilightPoints">
            <summary>
            Maximum number of points to use in Z0 (twilight zone).
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumStorage">
            <summary>
            Maximum number of storage area locations.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumFunctionDefinitions">
            <summary>
            Maximum number of function definitions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumInstructionDefinitions">
            <summary>
            Maximum number of instruction definitions.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumStackElements">
            <summary>
            Maximum stack depth.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumSizeOfInstructions">
            <summary>
            Maximum byte count for glpyh instructions
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumComponentElements">
            <summary>
            Maximum number of components at the top level for a composite glyph.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.MaximumComponentDepth">
            <summary>
            Maximum level of recursion. 1 for simple components.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Single,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.MaximumProfileTable"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable">
            <inheritdoc />
            <summary>
            A name table allows multilingual strings to be associated with the TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.FontName">
            <summary>
            Font name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.FontFamilyName">
            <summary>
            Font family name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.FontSubFamilyName">
            <summary>
            Font sub-family name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.NameRecords">
            <summary>
            The name records contained in this name table.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.String,System.String,System.String,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Fonts.TrueType.Names.TrueTypeNameRecord})">
            <summary>
            Creaye a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.NameTable.GetPostscriptName">
            <summary>
            Gets the PostScript name for the font if specified, preferring the Windows platform name if present.
            </summary>
            <returns>The PostScript name for the font if found or <see langword="null"/>.</returns>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table">
            <summary>
            Version 0 was defined in TrueType revision 1.5 and includes fields not in the Apple specification.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.TypographicAscender">
            <summary>
            Typographic ascender.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.TypographicDescender">
            <summary>
            Typographic descender.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.TypographicLineGap">
            <summary>
            Typographic line gap.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.WindowsAscent">
            <summary>
            The Windows ascender metric. This should be used to specify the height above the baseline for a clipping region. 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.WindowsDescent">
            <summary>
            The Windows descender metric. This should be used to specify the vertical extent below the baseline for a clipping region. 
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.UInt16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Collections.Generic.IReadOnlyList{System.Byte},System.Collections.Generic.IReadOnlyList{System.UInt32},System.String,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.UInt16,System.UInt16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2RevisedVersion0Table.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table">
            <inheritdoc cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.ITrueTypeTable"/>.
            <summary>
            The most basic format of the OS/2 table, excluding the fields not included in the Apple version of the specification.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.Version">
            <summary>
            The version number 0 - 5 detailing the layout of the OS/2 table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.XAverageCharacterWidth">
            <summary>
            The average width of all non-zero width characters in the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.WeightClass">
            <summary>
            Indicates the visual weight of characters in the font from 1 - 1000.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.WidthClass">
            <summary>
            The percentage difference from normal of the aspect ratio for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.TypeFlags">
            <summary>
            The font embedding licensing rights for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSubscriptXSize">
            <summary>
            The recommended horizontal size for subscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSubscriptYSize">
            <summary>
            The recommended vertical size for subscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSubscriptXOffset">
            <summary>
            The recommended horizontal offset (from the previous glyph origin to the subscript's origin) for subscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSubscriptYOffset">
            <summary>
            The recommended vertical offset (from the previous glyph origin to the subscript's origin) for subscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSuperscriptXSize">
            <summary>
            The recommended horizontal size for superscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSuperscriptYSize">
            <summary>
            The recommended vertical size for superscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSuperscriptXOffset">
            <summary>
            The recommended horizontal offset (from the previous glyph origin to the superscript's origin) for superscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YSuperscriptYOffset">
            <summary>
            The recommended vertical offset (from the previous glyph origin to the superscript's origin) for superscripts using this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YStrikeoutSize">
            <summary>
            Thickness of the strikeout stroke.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.YStrikeoutPosition">
            <summary>
            Position of the top of the strikeout stroke relative to the baseline.
            Positive values being above the baseline, negative values below.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.FamilyClass">
            <summary>
            Value registered by IBM for each font family to find substitutes.
            The high byte is the family class, the low byte is the family subclass.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.Panose">
            <summary>
            The PANOSE definition of 10 bytes defines various information about the
            font enabling matching fonts based on requirements. The meaning of each
            byte in the PANOSE definition depends on the preceding bytes. The first byte
            is the family type, Latin, Latin Hand Written, etc.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.UnicodeRanges">
            <summary>
            Specifies Unicode blocks supported by the font file for the Microsoft platform.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.VendorId">
            <summary>
            The four-character identifier for the vendor of the given type face.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.FontSelectionFlags">
            <summary>
            Contains information concerning the nature of the font patterns.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.FirstCharacterIndex">
            <summary>
            The minimum Unicode character code in this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.LastCharacterIndex">
            <summary>
            The maximum Unicode character code in this font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.UInt16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Collections.Generic.IReadOnlyList{System.Byte},System.Collections.Generic.IReadOnlyList{System.UInt32},System.String,System.UInt16,System.UInt16,System.UInt16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Table.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table">
            <summary>
            Version 1 was defined in TrueType revision 1.66. Version 1 has two additional fields beyond those in version 0. 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table.CodePage1">
            <summary>
            This field is used to specify the code pages encompassed by the font file in the 'cmap' subtable for the Microsoft platform(3), Unicode BMP encoding (1). 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table.CodePage2">
            <summary>
            This field is the second byte used to specify the code pages encompassed by the font file in the 'cmap' subtable for the Microsoft platform(3), Unicode BMP encoding (1). 
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.UInt16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Collections.Generic.IReadOnlyList{System.Byte},System.Collections.Generic.IReadOnlyList{System.UInt32},System.String,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.UInt16,System.UInt16,System.UInt32,System.UInt32)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version1Table.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable">
            <summary>
            Version 4 was defined in OpenType 1.5. Version 4 has the same fields as in version 2 and version 3. 
            Although new fields were not added beyond those in version 2 and 3, the specification of certain fields was revised. 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.XHeight">
            <summary>
            This metric specifies the distance between the baseline and the approximate height of non-ascending lowercase letters.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.CapHeight">
            <summary>
            This metric specifies the distance between the baseline and the approximate height of uppercase letters.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.DefaultCharacter">
            <summary>
            This is the Unicode code point, in UTF-16 encoding, of a character that can be used for a default glyph if a requested character is not supported. 
            If the value of this field is zero, glyph Id 0 is to be used for the default character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.BreakCharacter">
            <summary>
            This is the Unicode code point, in UTF-16 encoding, of a character that can be used as a default break character. 
            The break character is used to separate words and justify text. 
            Most fonts specify U+0020 SPACE as the break character.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.MaximumContext">
            <summary>
            The maximum distance in glyphs that any feature of this font is capable of effecting. For example
            kerning has a value of 2 (1 for each glyph in the kerning pair). 
            Fonts with the 'f f i' ligature would have a value of 3.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.UInt16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Collections.Generic.IReadOnlyList{System.Byte},System.Collections.Generic.IReadOnlyList{System.UInt32},System.String,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.UInt16,System.UInt16,System.UInt32,System.UInt32,System.Int16,System.Int16,System.UInt16,System.UInt16,System.UInt16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version2To4OpenTypeTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable">
            <summary>
            Version 5 was defined in OpenType 1.7. 
            Version 5 has two additional fields beyond those in versions 2 - 4.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable.LowerOpticalPointSize">
            <summary>
            This value is the lower value of the size range for which this font has been designed. 
            The units for this field are TWIPs (one-twentieth of a point, or 1440 per inch). 
            This is the inclusive lower bound.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable.UpperOpticalPointSize">
            <summary>
            This value is the upper value of the size range for which this font has been designed. 
            The units for this field are TWIPs (one-twentieth of a point, or 1440 per inch). 
            This is the exclusive upper bound.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.UInt16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Int16,System.Collections.Generic.IReadOnlyList{System.Byte},System.Collections.Generic.IReadOnlyList{System.UInt32},System.String,System.UInt16,System.UInt16,System.UInt16,System.Int16,System.Int16,System.Int16,System.UInt16,System.UInt16,System.UInt32,System.UInt32,System.Int16,System.Int16,System.UInt16,System.UInt16,System.UInt16,System.UInt16,System.UInt16)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.Os2Version5OpenTypeTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable">
            <inheritdoc />
            <summary>
            This table contains information for TrueType fonts on PostScript printers.
            This includes data for the FontInfo dictionary and the PostScript glyph names.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.Tag">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.DirectoryTable">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.Format">
            <summary>
            Format 1 contains the 258 standard Mac TrueType font file.<br/>
            Format 2 is the Microsoft font format.<br/>
            Format 2.5 is a space optimised subset of the standard Mac glyph set.<br/>
            Format 3 enables a special font type which provides no PostScript information.<br/>
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.ItalicAngle">
            <summary>
            Angle in counter-clockwise degrees from vertical. 0 for upright text, negative for right-leaning text.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.UnderlinePosition">
            <summary>
            Suggested values for the underline position with negative values below the baseline.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.UnderlineThickness">
            <summary>
            Suggested values for the underline thickness.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.IsFixedPitch">
            <summary>
            0 if the font is proportionally spaced, non-zero for monospace or other
            non-proportional spacing.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.MinimumMemoryType42">
            <summary>
            Minimum memory usage when the TrueType font is downloaded.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.MaximumMemoryType42">
            <summary>
            Maximum memory usage when the TrueType font is downloaded.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.MinimumMemoryType1">
            <summary>
            Minimum memory usage when the TrueType font is downloaded as a Type 1 font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.MaximumMemoryType1">
            <summary>
            Maximum memory usage when the TrueType font is downloaded as a Type 1 font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.GlyphNames">
            <summary>
            PostScript names of the glyphs.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable.#ctor(UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable,System.Single,System.Single,System.Int16,System.Int16,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.String[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.Tables.PostScriptTable"/>.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeChecksumCalculator">
            <summary>
            Calculates checksums for TrueType fonts.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeChecksumCalculator.CalculateWholeFontChecksum(UglyToad.PdfPig.Core.IInputBytes,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable)">
            <summary>
            Calculate the checksum for the whole font by setting checksum adjustment in the head table to 0.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeChecksumCalculator.Calculate(UglyToad.PdfPig.Core.IInputBytes,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable)">
            <summary>
            Calculate the checksum for the specific table.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeChecksumCalculator.Calculate(System.Collections.Generic.IEnumerable{System.Byte})">
            <summary>
            Calculate the TrueType checksum for the provided bytes.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes">
            <summary>
            Wraps the <see cref="T:UglyToad.PdfPig.Core.IInputBytes"/> to support reading TrueType data types.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.#ctor(System.Byte[])">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.#ctor(UglyToad.PdfPig.Core.IInputBytes)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.Position">
            <summary>
            The current position in the data.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.Length">
            <summary>
            The length of the data in bytes.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.Read32Fixed">
            <summary>
            Read a 32-fixed floating point value.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadSignedShort">
            <summary>
            Read a <see langword="short"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadUnsignedShort">
            <summary>
            Read a <see langword="ushort"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadByte">
            <summary>
            Read a <see langword="byte"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadTag">
            <summary>
            Reads the 4 character tag from the TrueType file.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.TryReadString(System.Int32,System.Text.Encoding,System.String@)">
            <summary>
            Read a <see langword="string"/> of the given number of bytes in length with the specified encoding.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadUnsignedInt">
            <summary>
            Read a <see langword="uint"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadSignedInt">
            <summary>
            Read an <see langword="int"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadLong">
            <summary>
            Read a <see langword="long"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadInternationalDate">
            <summary>
            Read a <see cref="T:System.DateTime"/> from the data in UTC time.
            In TrueType dates are specified as the number of seconds since 1904-01-01.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.Seek(System.Int64)">
            <summary>
            Move to the specified position in the data.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadSignedByte">
            <summary>
            Read an <see langword="int"/> which represents a signed byte.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadUnsignedShortArray(System.Int32)">
            <summary>
            Read an array of <see langword="ushort"/>s with the specified number of values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadByteArray(System.Int32)">
            <summary>
            Read an array of <see langword="byte"/>s with the specified number of values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadUnsignedIntArray(System.Int32)">
            <summary>
            Read an array of <see langword="uint"/>s with the specified number of values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ReadShortArray(System.Int32)">
            <summary>
            Read an array of <see langword="short"/>s with the specified number of values.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeDataBytes.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont">
            <summary>
            A TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.Version">
            <summary>
            The font version number.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TableHeaders">
            <summary>
            The table directory, entries indicate the offset and length of the data for a given table name.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TableRegister">
            <summary>
            The actual table data parsed for this TrueType font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.Name">
            <summary>
            The name of the font according to the font's name table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.WindowsUnicodeCMap">
            <summary>
            The cmap subtable for Windows Unicode (3, 1).
            Can be <see langword="null" />.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.MacRomanCMap">
            <summary>
            The cmap subtable for Mac Roman (1, 0).
            Can be <see langword="null" />.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.WindowsSymbolCMap">
            <summary>
            The cmap subtable for Windows Symbol (3, 0).
            Can be <see langword="null" />.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.NumberOfTables">
            <summary>
            The number of tables in this font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.#ctor(System.Single,System.Collections.Generic.IReadOnlyDictionary{System.String,UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable},UglyToad.PdfPig.Fonts.TrueType.Parser.TableRegister)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetBoundingBox(System.Int32,UglyToad.PdfPig.Core.PdfRectangle@)">
            <summary>
            Try to get the bounding box for a glyph representing the specified character code if present.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetBoundingBox(System.Int32,System.Func{System.Int32,System.Nullable{System.Int32}},UglyToad.PdfPig.Core.PdfRectangle@)">
            <summary>
            Try to get the bounding box for a glyph representing the specified character code if present.
            Uses a custom mapping of character code to glyph index.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetPath(System.Int32,System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Core.PdfSubpath}@)">
            <summary>
            Try to get the bounding box for a glyph representing the specified character code if present.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetPath(System.Int32,System.Func{System.Int32,System.Nullable{System.Int32}},System.Collections.Generic.IReadOnlyList{UglyToad.PdfPig.Core.PdfSubpath}@)">
            <summary>
            Try to get the path for a glyph representing the specified character code if present.
            Uses a custom mapping of character code to glyph index.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetAdvanceWidth(System.Int32,System.Double@)">
            <summary>
            Try to get the advance width for a glyph representing the specified character code if present.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.TryGetAdvanceWidth(System.Int32,System.Func{System.Int32,System.Nullable{System.Int32}},System.Double@)">
            <summary>
            Try to get the advance width for a glyph representing the specified character code if present.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeFont.GetUnitsPerEm">
            <summary>
            Get the number of units per em for this font.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable">
            <inheritdoc cref="T:UglyToad.PdfPig.Core.IWriteable" />
            <summary>
            A table directory entry from the TrueType font file. Indicates the position of the corresponding table
            data in the TrueType font.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Cmap">
            <summary>
            Character to glyph mapping.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Glyf">
            <summary>
            Glyph data.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Head">
            <summary>
            Font header.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Hhea">
            <summary>
            Horizontal header.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Hmtx">
            <summary>
            Horizontal metrics.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Loca">
            <summary>
            Index to location.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Maxp">
            <summary>
            Maximum profile.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Name">
            <summary>
            Naming table.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Post">
            <summary>
            PostScript information.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Os2">
            <summary>
            OS/2 and Windows specific metrics.
            </summary>
            <remarks>Required</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Cvt">
            <summary>
            Control Value Table.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Ebdt">
            <summary>
            Embedded bitmap data.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Eblc">
            <summary>
            Embedded bitmap location data.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Ebsc">
            <summary>
            Embedded bitmap scaling data.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Fpgm">
            <summary>
            Font program.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Gasp">
            <summary>
            Grid-fitting and scan conversion procedure (grayscale).
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Hdmx">
            <summary>
            Horizontal device metrics.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Kern">
            <summary>
            Kerning.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Ltsh">
            <summary>
            Linear threshold title.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Prep">
            <summary>
            CVT program.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Pclt">
            <summary>
            PCL5.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Vdmx">
            <summary>
            Vertical device metrics.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Vhea">
            <summary>
            Vertical metrics header.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Vmtx">
            <summary>
            Vertical metrics.
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Cff">
            <summary>
            Compact font format table. The corresponding table contains a Compact Font Format font representation 
            (also known as a PostScript Type 1, or CIDFont).
            </summary>
            <remarks>Optional</remarks>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Tag">
            <summary>
            The 4 byte tag identifying the table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.CheckSum">
            <summary>
            The checksum for the table.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Offset">
            <summary>
            Offset of the table data from the beginning of the file in bytes.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Length">
            <summary>
            The length of the table data in bytes.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.#ctor(System.String,System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.GetEmptyHeaderTable(System.String)">
            <summary>
            Gets an empty header table with the non-tag values set to zero.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.Write(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.TrueTypeHeaderTable.ToString">
            <inheritdoc />
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.WindowsGlyphList4.NumberOfMacGlyphs">
            <summary>
            The number of standard mac glyph names.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.WindowsGlyphList4.MacGlyphNames">
            <summary>
            The 258 standard mac glyph names used in 'post' format 1 and 2.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.TrueType.WindowsGlyphList4.MacGlyphNamesIndices">
            <summary>
            The indices of the standard mac glyph names.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.TrueType.WindowsGlyphList4.#cctor">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.CallOtherSubrCommand">
            <summary>
            Call other subroutine command. Arguments are pushed onto the PostScript interpreter operand stack then
            the PostScript language procedure at the other subroutine index in the OtherSubrs array in the Private dictionary
            (or a built-in function equivalent to this procedure) is executed.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.CallSubrCommand">
            <summary>
            Calls a subroutine with index from the subroutines array in the Private dictionary.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.DivCommand">
            <summary>
            This operator returns the result of dividing num1 by num2. The result is always a real.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.PopCommand">
            <summary>
            Pops a number from the top of the PostScript interpreter operand stack and pushes that number onto the operand stack.
            This command is used only to retrieve a result from an OtherSubrs procedure.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.ReturnCommand">
            <summary>
            Returns from a charstring subroutine and continues execution in the calling charstring.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Arithmetic.SetCurrentPointCommand">
            <summary>
            Sets the current point to (x, y) in absolute character space coordinates without performing a charstring moveto command.
            <para>This establishes the current point for a subsequent relative path building command.
            The 'setcurrentpoint' command is used only in conjunction with results from 'OtherSubrs' procedures.</para>
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Hint.DotSectionCommand">
            <summary>
            Brackets an outline section for the dots in letters such as "i", "j" and "!".
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Hint.HStem3Command">
            <summary>
            Declares the vertical ranges of three horizontal stem zones:
            1st: between y0 and y0 + dy0
            2nd: between y1 and y1 + dy1
            3rd: between y2 and y2 + dy2
            Where y0, y1 and y2 are all relative to the y coordinate of the left sidebearing point.
            </summary>
            <remarks>
            Suited to letters with 3 horizontal stems like 'E'.
            </remarks>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Hint.HStemCommand">
            <summary>
            Declares the vertical range of a horizontal stem zone between the y coordinates y and y+dy,
            where y is relative to the y coordinate of the left sidebearing point.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Hint.VStem3Command">
            <summary>
            Declares the horizontal ranges of three vertical stem zones.
            1st: between x0 and x0 + dx0
            2nd: between x1 and x1 + dx1
            3rd: between x2 and x2 + dx2
            Where x0, x1 and x2 are all relative to the x coordinate of the left sidebearing point.
            </summary>
            <remarks>
            Suited to letters with 3 vertical stems, for instance 'm'.
            </remarks>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.Hint.VStemCommand">
            <summary>
            Declares the horizontal range of a vertical stem zone between the x coordinates x and x+dx,
            where x is relative to the x coordinate of the left sidebearing point.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.LazyType1Command">
            <summary>
            Represents the deferred execution of a Type 1 Build Char command.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.ClosePathCommand">
            <summary>
            Closes a sub-path. This command does not reposition the current point.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.HLineToCommand">
            <summary>
            Horizontal line-to command.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.HMoveToCommand">
            <summary>
            Relative move to for horizontal dimension only.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.HvCurveToCommand">
            <summary>
            Horizontal vertical curve to command. Draws a Bézier curve when the first Bézier tangent is horizontal and the second Bézier tangent is vertical.
            Equivalent to dx1 0 dx2 dy2 0 dy3 rrcurveto.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.RelativeRCurveToCommand">
            <summary>
            Relative rcurveto. Whereas the arguments to the rcurveto operator in the PostScript language are all relative to the current
            point, the arguments to rrcurveto are relative to each other. 
            Equivalent to: dx1 dy1 (dx1+dx2) (dy1+dy2) (dx1+dx2+dx3) (dy1+dy2+dy3) rcurveto.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.RLineToCommand">
            <summary>
            Relative line-to command. Creates a line moving a distance relative to the current point.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.RMoveToCommand">
            <summary>
            Relative move to command. starts a new subpath of the current path in the same manner as moveto.
            However, the number pair is interpreted as a displacement relative to the current point (x, y) rather than as an absolute coordinate.
            </summary>
            <remarks>
            moveto: moveto sets the current point in the graphics state to the user space coordinate (x, y) without adding any line segments to the current path.
            If the previous path operation in the current path was also a moveto or rmoveto, 
            that point is deleted from the current path and the new moveto point replaces it.
            </remarks>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.VhCurveToCommand">
            <summary>
            Vertical-horizontal curveto. 
            Equivalent to 0 dy1 dx2 dy2 dx3 0 rrcurveto.
            This command eliminates two arguments from an rrcurveto call when the first Bézier tangent is vertical and the second Bézier tangent is horizontal.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.VLineToCommand">
            <summary>
            Vertical-line to command.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.PathConstruction.VMoveToCommand">
            <summary>
            Vertical move to. Moves relative to the current point.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.StartFinishOutline.EndCharCommand">
            <summary>
            Finishes a charstring outline definition and must be the last command in a character's outline
            (except for accented characters defined using seac).
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.StartFinishOutline.HsbwCommand">
            <summary>
            The name hsbw stands for horizontal sidebearing and width; 
            horizontal indicates that the y component of both the sidebearing and width is 0. 
            This command sets the left sidebearing point at (sbx, 0) and sets the character width vector to(wx, 0) in character space.
            This command also sets the current point to (sbx, 0), but does not place the point in the character path.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.StartFinishOutline.SbwCommand">
            <summary>
            Sets left sidebearing and the character width vector.
            This command also sets the current point to(sbx, sby), but does not place the point in the character path.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Commands.StartFinishOutline.SeacCommand">
            <summary>
            Standard encoding accented character.
            Makes an accented character from two other characters in the font program.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.CharStrings.Type1CharStringParser">
            <summary>
            Decodes a set of CharStrings to their corresponding Type 1 BuildChar operations.
            </summary>
            <remarks>
            <para>
            A charstring is an encrypted sequence of unsigned 8-bit bytes that encode integers and commands.
            Type 1 BuildChar, when interpreting a charstring, will first decrypt it and then will decode
            its bytes one at a time in sequence.
            </para>
            <para>
            The value in a byte indicates a command, a number, or subsequent bytes that are to be interpreted
            in a special way.
            </para>
            <para>
            Once the bytes are decoded into numbers and commands, the execution of these numbers and commands proceeds in a
            manner similar to the operation of the PostScript language. Type 1 BuildChar uses its own operand stack,
            called the Type 1 BuildChar operand stack, that is distinct from the PostScript interpreter operand stack.
            </para>
            <para>
            This stack holds up to 24 numeric entries. A number, decoded from a charstring, is pushed onto the Type 1
            BuildChar operand stack. A command expects its arguments in order on this operand stack with all arguments generally taken
            from the bottom of the stack (first argument bottom-most);
            however, some commands, particularly the subroutine commands, normally work from the top of the stack. If a command returns
            results, they are pushed onto the Type 1 BuildChar operand stack (last result topmost).
            </para>
            </remarks>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.CharStrings.Type1CharStrings.CommandSequence.Commands">
            <summary>
            The ordered list of numbers and commands for a Type 1 charstring or subroutine.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.MinFeature">
            <summary>
            Represents the <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary"/> MinFeature entry which is required for compatibility
            and is required by the specification to have the value 16, 16.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.MinFeature.First">
            <summary>
            The first value.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.MinFeature.Second">
            <summary>
            The second value.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.MinFeature.Default">
            <summary>
            The required default value of <see cref="T:UglyToad.PdfPig.Fonts.Type1.MinFeature"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.MinFeature.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a <see cref="T:UglyToad.PdfPig.Fonts.Type1.MinFeature"/> array.
            </summary>
            <param name="first">The first value.</param>
            <param name="second">The second value.</param>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.MinFeature.ToString">
            <inheritdoc />
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Parser.Type1ArrayTokenizer">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Parser.Type1ArrayTokenizer.ReadsNextByte">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Parser.Type1ArrayTokenizer.TryTokenize(System.Byte,UglyToad.PdfPig.Core.IInputBytes,UglyToad.PdfPig.Tokens.IToken@)">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Parser.Type1EncryptedPortionParser.IsBinary(System.Collections.Generic.IReadOnlyList{System.Byte})">
            <summary>
            To distinguish between binary and hex the first 4 bytes (of the ciphertext) for hex must obey these restrictions:
            The first byte must not be whitespace.
            One of the first four ciphertext bytes must not be an ASCII hex character.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Parser.Type1FontParser">
            <summary>
            Parse Adobe Type 1 font format.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Parser.Type1FontParser.Parse(UglyToad.PdfPig.Core.IInputBytes,System.Int32,System.Int32)">
            <summary>
            Parses an embedded Adobe Type 1 font file.
            </summary>
            <param name="inputBytes">The bytes of the font program.</param>
            <param name="length1">The length in bytes of the clear text portion of the font program.</param>
            <param name="length2">The length in bytes of the encrypted portion of the font program.</param>
            <returns>The parsed type 1 font.</returns>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Parser.Type1FontParser.ReadPfbHeader(UglyToad.PdfPig.Core.IInputBytes)">
            <summary>
            Where an entire PFB file has been embedded in the PDF we read the header first.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Parser.Type1NameTokenizer">
            <inheritdoc />
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Parser.Type1NameTokenizer.ReadsNextByte">
            <inheritdoc />
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Parser.Type1NameTokenizer.TryTokenize(System.Byte,UglyToad.PdfPig.Core.IInputBytes,UglyToad.PdfPig.Tokens.IToken@)">
            <inheritdoc />
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Type1.Parser.Type1Symbols.Erode">
            <summary>
            This is shown briefly in the Type 1 spec but nowhere is it specified what it means or does.
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Type1.Parser.Type1Token.TokenType.StartArray">
            <summary>
            An array must begin with either '[' or '{'. 
            </summary>
        </member>
        <member name="F:UglyToad.PdfPig.Fonts.Type1.Parser.Type1Token.TokenType.EndArray">
            <summary>
            An array must end with either ']' or '}'. 
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Type1Font">
            <summary>
            An Adobe Type 1 font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.Name">
            <summary>
            The name of the font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.Encoding">
            <summary>
            The encoding dictionary defining a name for each character code.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.FontMatrix">
            <summary>
            The font matrix.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.BoundingBox">
            <summary>
            A rectangle in glyph coordinates specifying the font bounding box.
            This is the smallest rectangle enclosing the shape that would result if all of the glyphs were overlayed on each other. 
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.PrivateDictionary">
            <summary>
            The private dictionary for this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1Font.CharStrings">
            <summary>
            The charstrings in this font.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1Font.#ctor(System.String,System.Collections.Generic.IReadOnlyDictionary{System.Int32,System.String},UglyToad.PdfPig.Tokens.ArrayToken,UglyToad.PdfPig.Core.PdfRectangle,UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary,UglyToad.PdfPig.Fonts.Type1.CharStrings.Type1CharStrings)">
            <summary>
            Create a new <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1Font"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1Font.GetCharacterBoundingBox(System.String)">
            <summary>
            Get the bounding box for the character with the given name.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1Font.ContainsNamedCharacter(System.String)">
            <summary>
            Whether the font contains a character with the given name.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1Font.GetCharacterPath(System.String)">
            <summary>
            Get the pdfpath for the character with the given name.
            </summary>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary">
            <summary>
            The Private dictionary for a Type 1 font contains hints that apply across all characters in the font. These hints
            help preserve properties of character outline shapes when rendered at smaller sizes and lower resolutions.
            These hints help ensure that the shape is as close as possible to the original design even where the character
            must be represented in few pixels.
            Note that subroutines are also defined in the private dictionary however for the purposes of this API they are
            stored on the parent <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1Font"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.UniqueId">
            <summary>
            Optional: Uniquely identifies this font.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.LenIv">
            <summary>
            Optional: Indicates the number of random bytes used for charstring encryption/decryption.
            Default: 4
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.RoundStemUp">
            <summary>
            Optional: Preserved for backwards compatibility. Must be set if the <see cref="P:UglyToad.PdfPig.Fonts.AdobeStylePrivateDictionary.LanguageGroup"/> is 1.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Password">
            <summary>
            Required: Backwards compatibility.
            Default: 5839
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.MinFeature">
            <summary>
            Required: Backwards compatibility.
            Default: {16 16}
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.#ctor(UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder)">
            <inheritdoc />
            <summary>
            Creates a new <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary" />.
            </summary>
            <param name="builder">The builder used to gather property values.</param>
        </member>
        <member name="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder">
            <summary>
            A mutable builder which can set any property of the private dictionary and performs no validation.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.Rd">
            <summary>
            Temporary storage for the Rd procedure tokens.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.NoAccessPut">
            <summary>
            Temporary storage for the No Access Put procedure tokens.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.NoAccessDef">
            <summary>
            Temporary storage for the No Access Def procedure tokens.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.Subroutines">
            <summary>
            Temporary storage for the decrypted but raw bytes of the subroutines in this private dictionary.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.OtherSubroutines">
            <summary>
            Temporary storage for the tokens of the other subroutine procedures.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.UniqueId">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.UniqueId"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.Password">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Password"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.LenIv">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.LenIv"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.MinFeature">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.MinFeature"/>.
            </summary>
        </member>
        <member name="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.RoundStemUp">
            <summary>
            <see cref="P:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.RoundStemUp"/>.
            </summary>
        </member>
        <member name="M:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary.Builder.Build">
            <summary>
            Generate a <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary"/> from the values in this builder.
            </summary>
            <returns>The generated <see cref="T:UglyToad.PdfPig.Fonts.Type1.Type1PrivateDictionary"/>.</returns>
        </member>
    </members>
</doc>
